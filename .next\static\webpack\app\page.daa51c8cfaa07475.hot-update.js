"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/faq/FAQSection.tsx":
/*!***************************************!*\
  !*** ./components/faq/FAQSection.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQSection: function() { return /* binding */ FAQSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/faqService */ \"(app-pages-browser)/./lib/services/faqService.ts\");\n/* harmony import */ var _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ FAQSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * Mapeo de iconos para temas municipales\n */ const themeIcons = {\n    Receipt: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    FileCheck: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Award: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    Zap: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    FileText: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    CreditCard: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    HelpCircle: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\n/**\n * Componente principal de la sección FAQ\n */ function FAQSection(param) {\n    let { title = \"Preguntas Frecuentes\", description = \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre tr\\xe1mites y servicios municipales\", initialLimit = 10, showSearch = true, showCategoryFilter = true, showStats = true, className = \"\" } = param;\n    _s();\n    // Estados\n    const [faqs, setFaqs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [themes, setThemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTheme, setSelectedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedFAQs, setExpandedFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAllFAQs, setShowAllFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalFAQs: 0,\n        totalThemes: 0,\n        averagePopularity: 0,\n        mostPopularTheme: \"\"\n    });\n    // Cargar datos iniciales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    // Realizar búsqueda cuando cambie la query o tema\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (searchQuery.trim() || selectedTheme) {\n            performSearch();\n        }\n    }, [\n        searchQuery,\n        selectedTheme,\n        showAllFAQs,\n        initialLimit\n    ]);\n    /**\n   * Cargar datos iniciales\n   */ const loadInitialData = async ()=>{\n        const startTime = Date.now();\n        try {\n            setIsLoading(true);\n            const [themesData, popularFAQs, statsData] = await Promise.all([\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getThemes(),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(initialLimit),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQStats()\n            ]);\n            setThemes(themesData);\n            setFaqs(popularFAQs);\n            setStats(statsData);\n            // Registrar carga de sección\n            const loadTime = Date.now() - startTime;\n            _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackSectionLoad(\"faq-section\", loadTime);\n        } catch (error) {\n            console.error(\"Error loading FAQ data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Realizar búsqueda de FAQs\n   */ const performSearch = async ()=>{\n        try {\n            if (searchQuery.trim()) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].searchFAQs(searchQuery, {\n                    theme: selectedTheme || undefined,\n                    limit: showAllFAQs ? 50 : initialLimit\n                });\n                setFaqs(results);\n            } else if (selectedTheme) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQsByTheme(selectedTheme, showAllFAQs ? undefined : initialLimit);\n                setFaqs(results);\n            } else {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(showAllFAQs ? 50 : initialLimit);\n                setFaqs(results);\n            }\n        } catch (error) {\n            console.error(\"Error searching FAQs:\", error);\n        }\n    };\n    /**\n   * Alternar expansión de FAQ\n   */ const toggleFAQExpansion = (faqId)=>{\n        const newExpanded = new Set(expandedFAQs);\n        if (newExpanded.has(faqId)) {\n            newExpanded.delete(faqId);\n        } else {\n            newExpanded.add(faqId);\n            // Registrar visualización cuando se expande\n            const faq = faqs.find((f)=>f.id === faqId);\n            if (faq) {\n                _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackFAQView(faq.id, faq.question);\n            }\n        }\n        setExpandedFAQs(newExpanded);\n    };\n    /**\n   * Limpiar filtros\n   */ const clearFilters = ()=>{\n        setSearchQuery(\"\");\n        setSelectedTheme(\"\");\n        setShowAllFAQs(false);\n    };\n    /**\n   * Obtener icono de tema basado en el nombre del tema\n   */ const getThemeIcon = (themeName)=>{\n        // Mapear nombres de temas a iconos apropiados\n        const themeIconMap = {\n            \"CERTIFICADO\": \"FileCheck\",\n            \"IMPUESTOS\": \"Receipt\",\n            \"LICENCIAS\": \"Award\",\n            \"PERMISOS\": \"FileText\",\n            \"PAGOS\": \"CreditCard\",\n            \"SERVICIOS\": \"Zap\"\n        };\n        // Buscar coincidencia parcial en el nombre del tema\n        const iconKey = Object.keys(themeIconMap).find((key)=>themeName.toUpperCase().includes(key));\n        const IconComponent = iconKey ? themeIcons[themeIconMap[iconKey]] : themeIcons.HelpCircle;\n        return IconComponent;\n    };\n    // FAQs filtrados y limitados\n    const displayedFAQs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return showAllFAQs ? faqs : faqs.slice(0, initialLimit);\n    }, [\n        faqs,\n        showAllFAQs,\n        initialLimit\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-chia-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Cargando preguntas frecuentes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-8 w-8 text-chia-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalFAQs,\n                                    \" preguntas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalThemes,\n                                    \" temas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"M\\xe1s consultado: \",\n                                    stats.mostPopularTheme\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            (showSearch || showCategoryFilter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"Buscar en preguntas frecuentes...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 19\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, this),\n                            showCategoryFilter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filtrar por categor\\xeda:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: selectedTheme === \"\" ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSelectedTheme(\"\"),\n                                                className: \"h-8\",\n                                                children: \"Todos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this),\n                                            themes.map((theme)=>{\n                                                const IconComponent = getThemeIcon(theme.name);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedTheme === theme.id ? \"default\" : \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setSelectedTheme(theme.id);\n                                                        _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackCategoryFilter(theme.id);\n                                                    },\n                                                    className: \"h-8 space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: theme.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"ml-1 h-4 text-xs\",\n                                                            children: theme.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, theme.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 17\n                            }, this),\n                            (searchQuery || selectedTheme) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: clearFilters,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Limpiar filtros\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: displayedFAQs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No se encontraron preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: searchQuery ? 'No hay resultados para \"'.concat(searchQuery, '\"') : \"No hay preguntas disponibles en esta categor\\xeda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this),\n                            (searchQuery || selectedTheme) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: clearFilters,\n                                children: \"Ver todas las preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        displayedFAQs.map((faq)=>{\n                            var _category;\n                            const isExpanded = expandedFAQs.has(faq.id);\n                            const theme = themes.find((t)=>t.id === faq.themeId);\n                            const IconComponent = theme ? getThemeIcon(theme.name) : _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"transition-all duration-200 hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"cursor-pointer\",\n                                        onClick: ()=>toggleFAQExpansion(faq.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-4 w-4 text-chia-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: (_category = category) === null || _category === void 0 ? void 0 : _category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        faq.popularityScore,\n                                                                        \"% popular\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-left text-lg leading-tight\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"ml-4 flex-shrink-0\",\n                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 19\n                                    }, this),\n                                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 25\n                                                }, this),\n                                                faq.keywords.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: faq.keywords.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: keyword\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 27\n                                                }, this),\n                                                faq.relatedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Tr\\xe1mites relacionados:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: faq.relatedProcedures.map((procedure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-chia-blue-600\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        procedure\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 33\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, faq.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        !showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowAllFAQs(true),\n                                className: \"px-8\",\n                                children: [\n                                    \"Ver m\\xe1s preguntas (\",\n                                    faqs.length - initialLimit,\n                                    \" restantes)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 15\n                        }, this),\n                        showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                onClick: ()=>setShowAllFAQs(false),\n                                className: \"px-8\",\n                                children: \"Ver menos preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQSection, \"fsnOQ7wz/gQXT5txZJwtK7fPzB8=\");\n_c = FAQSection;\nvar _c;\n$RefreshReg$(_c, \"FAQSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/faq/FAQSection.tsx\n"));

/***/ })

});