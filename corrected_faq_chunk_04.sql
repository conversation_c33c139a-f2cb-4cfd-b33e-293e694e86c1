-- Corrected FAQ Chunk 4
-- Questions 46 to 60

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene algún costo esta autorización?', 'NO TIENE COSTO', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene', 'algún', 'esta', 'autorización'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los servicios que presta el equipo de Gestión del Riesgo?', 'Gestiona, administra, direcciona y monitorea diferentes escenarios de riesgo que se puedan presentar ante emergencias inminentes y no inminentes. También brindamos capacitaciones, charlas y asistencias técnicas.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['direccion', 'servicio', 'asistencia', 'cuáles', 'servicios', 'presta', 'equipo', 'gestión'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En dónde puedo reportar una emergencia?', 'Se debe realizar exclusivamente a la línea de atención 123 Ante un supuesto riesgo (no inminente): Hay un árbol fuera/dentro de mi casa y/o predio, que está en riesgo de caída. ¿Qué debo hacer? Debe realizar la solicitud que incluya sus datos de contacto, ubicación urbana o rural.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['solicitud', 'dónde', 'puedo', 'reportar', 'emergencia', 'debe'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtendré la respuesta?', 'Al término de quince días hábiles se habrá atendido su solicitud. Hay un(os) panal(es) de abejas en el lugar “x”, deseo que lo(s) retiren, ¿Qué debo hacer para tramitar mi solicitud? Por favor ingrese su nombre completo Número y tipo de identificación Ubicación del lugar Trasladaremos la petición al área encargada Encontré una zarigüeya en uno de los arbustos de mi jardín, ¿Qué hago para que la recojan? La misma respuesta que en la pregunta No. 2 Otras inquietudes que podrían tener los ciudadanos, vinculadas a las líneas de atención que maneja éste equipo de GRD: Quisiera que me agenden una capacitación en primeros auxilios, ¿Cuál es la ruta, los requisitos?', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'solicitud', 'cuánto', 'tiempo', 'obtendré', 'respuesta', 'término'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿A quién va dirigida la capacitación pregunta IA?: otra dependencia, institución educativa, colegio, otros. Elija una opción', 'Ingrese el número de personas a las cuáles va dirigida la capacitación Al término de quince días hábiles se dará respuesta a su solicitud.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['solicitud', 'quién', 'dirigida', 'capacitación', 'pregunta', 'otra'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué requisitos se deben cumplir para realizar un evento en el municipio?', 'Antes de la radicación del evento se deben adelantar estos requisitos: Debe presentar el plan de emergencia y contingencia <NAME_EMAIL> para que por parte de la Dirección Centro de Atención al Ciudadano se remita a bomberos y estos agenden la inspección, posteriormente remitir el informe de la inspección que adelantó Bomberos al correo <NAME_EMAIL> para que por parte de la Dirección Centro de Atención al Ciudadano se remita a la Secretaría de Gobierno – Gestión del Riesgo – Comité de Conocimiento y Prevención del Riesgo Para Eventos Masivos y No Masivos y así obtener el concepto favorable. Solicitar concepto favorable de la Secretaría de Salud diligenciando este link: https://forms.office.com/r/cuiu22nmVw Obtener concepto favorable de la Secretaría de Movilidad de Chía, radicando el Plan de Manejo de Tránsito (PMT) a <EMAIL> Enviar solicitud a la Policía Nacional para acompañamiento y control del evento en caso de ser necesario <NAME_EMAIL> Si el evento tiene recorrido, desfiles y/o similares, solicitar por escrito el permiso del evento y obtener respuesta de autorización por parte de la Secretaría de Movilidad. Si el evento es en sitio, presentar el Plan de Manejo de Tránsito (PMT) a la Secretaría de Tránsito y obtener el concepto favorable con anticipación. SI EN EL EVENTO SE LLEVARÁ A CABO RECORRIDO Y EVENTO EN SITIO SE DEBERÁN TRAMITAR LOS DOS PERMISOS – (permisos de los numerales 3 y 5). Cuando ya cuente con las anteriores autorizaciones, se radicará el trámite ante el Comité de Eventos como mínimo con un (1) mes antes de la realización del evento. La documentación que debes presentar es: Cédula del Representante Legal Plan de Emergencia y Contingencia Carta de intención del evento Concepto Favorable de la inspección adelantada por Bomberos Concepto Favorable de la inspección adelantada por Secretaría de Salud Concepto Favorable de la Inspección adelantada por Secretaría de Movilidad Presentar Paz y Salvo de pago de Derechos de Autor autorizadas por la Dirección Nacional de Derechos de Autor Póliza de Responsabilidad Civil Extracontractual que ampare la seguridad de las personas y de los elementos Anexar pantallazo de la solicitud a la Policía Nacional, en caso de requerirlo Si el evento es de espectáculos públicos, es decir, música, danza, circo sin animales, teatro, magia, debes presentar adicional: Certificados de contribución parafiscal Certificado de productor Ficha PULEP En caso de edificaciones nuevas, contar con un Concepto Técnico e Comportamiento Estructural y Funcional del escenario. NOTA: Se entenderá como radicado del evento un (1) solo correo que recopile toda la documentación requerida.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'certificado', 'solicitud', 'requisitos', 'deben', 'cumplir', 'para', 'realizar'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtengo la autorización?', 'El tiempo de respuesta para este trámite es de máximo quince (15) días.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuánto', 'tiempo', 'obtengo', 'autorización', 'tiempo'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué debo hacer para radicar la solicitud?', 'Una vez reunida toda la documentación requerida, puedes radicar la solicitud en la ventanilla o <NAME_EMAIL>', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['solicitud', 'debo', 'hacer', 'para', 'radicar'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene algún costo esta autorización?', 'NO TIENE COSTO', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene', 'algún', 'esta', 'autorización'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste la red cívica?', 'Es un cuerpo no armado de carácter civil, sin ánimo de lucro, constituido voluntariamente con el objeto de prestar apoyo para el cumplimiento de unas misiones específicas de carácter educativo, social y comunitario que realiza la Policía Nacional con el propósito de fortalecer las relaciones entre la policía y la comunidad, cuenta con más de 28 personas de apoyo disponibles las 24 horas del día, que se distribuyen en 3 turnos de 8 horas que están disponibles para el apoyo a la Policía Nacional Filosofía: Tiene como prioridad contribuir en la construcción y fortalecimiento del tejido social, los principios de convivencia y la cultura de la legalidad, a través de acciones preventivas, educativas y las enmarcadas en el ámbito social, desarrolladas por ciudadanos voluntarios convencidos de su importancia y aplicabilidad.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['apoyo', 'consiste', 'cívica', 'cuerpo', 'armado', 'carácter'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo puedo hacer parte de la red cívica de mayores?', 'Ser colombiano o extranjero nacionalizado Tener la mayoría de edad Acreditar título profesional Tener definida la situación militar No registrar antecedentes penales ni Disciplinarios No tener antecedentes en el registro nacional de medidas correctivas Acreditar afiliación al sistema de seguridad social Aprobar la verificación administrativa de la información practicada por la policía Nacional Superar la entrevista personal que realiza el respectivo oficial comandante y un psicólogo', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['registro', 'cómo', 'puedo', 'hacer', 'parte', 'cívica'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Existe una convocatoria para hacer parte de la cívica?', 'En el momento se encuentran abiertas las convocatorias para hacer parte de la RED CÍVICA, esto, hasta completar cupos habilitados', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['existe', 'convocatoria', 'para', 'hacer', 'parte'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la diferencia entre el servicio que presta la policía Nacional de chía y el que presta la red cívica?', 'La red cívica está conformada por particulares que prestan un apoyo administrativo y no tienen funciones policiales por ley, ni funciones de autoridad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la documentación que se requiere para hacer parte de la red cívica infantil?', '1. Ficha de inscripción- la policía facilita el formato 2. ⁠carta de autorización-la policía facilita el formato 3. ⁠fotocopia cédula de los padres 4. ⁠fotocopia de la tarjeta de identidad del niño(a) 5. ⁠fotocopia del carnet de vacunas 6. ⁠fotocopia del certificado de salud 7. ⁠fotocopia del certificado del médico general 8. ⁠fotocopia certificado estudiantil 9. ⁠2 fotos 3*4 fondo azul 10. ⁠todo en una carpeta blanca 4 alas', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['certificado', 'cuál', 'documentación', 'requiere', 'para', 'hacer'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se va a distribuir la red cívica en el municipio?', 'La RED CÍVICA estará disponible las 24 horas del día en 3 turnos que estarán haciendo rondas constantes en el municipio para velar por la seguridad del Municipio de Chía, se van a distribuir de la siguiente manera 2 Motorizados Cerca de Piedra y Fonquetá incluido los cerros 2 Motorizados Tíquiza y Fagua incluido los cerros 2 Motorizados Mercedes de Calahorra 2 Motorizados Bojacá 2 Motorizados Zona Centro 2 Motorizados La Balsa 2 Motorizados Samaria 2 Motorizados Fusca, Yerbabuena, corredor vial Centro Chía - Limite Fontanar y Sector Colombia - MC Donalds De esta manera la CIVICA estará brindando apoyo contante a la Policía del municipio', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['apoyo', 'cómo', 'distribuir', 'cívica', 'municipio', 'cívica'], 6);
