"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/faq/FAQSection.tsx":
/*!***************************************!*\
  !*** ./components/faq/FAQSection.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQSection: function() { return /* binding */ FAQSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/faqService */ \"(app-pages-browser)/./lib/services/faqService.ts\");\n/* harmony import */ var _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ FAQSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * Mapeo de iconos para categorías\n */ const categoryIcons = {\n    Receipt: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    FileCheck: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Award: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    Zap: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    FileText: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    CreditCard: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    HelpCircle: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\n/**\n * Componente principal de la sección FAQ\n */ function FAQSection(param) {\n    let { title = \"Preguntas Frecuentes\", description = \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre tr\\xe1mites y servicios municipales\", initialLimit = 10, showSearch = true, showCategoryFilter = true, showStats = true, className = \"\" } = param;\n    _s();\n    // Estados\n    const [faqs, setFaqs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [themes, setThemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTheme, setSelectedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedFAQs, setExpandedFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAllFAQs, setShowAllFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalFAQs: 0,\n        totalThemes: 0,\n        averagePopularity: 0,\n        mostPopularTheme: \"\"\n    });\n    // Cargar datos iniciales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    // Realizar búsqueda cuando cambie la query o tema\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (searchQuery.trim() || selectedTheme) {\n            performSearch();\n        }\n    }, [\n        searchQuery,\n        selectedTheme,\n        showAllFAQs,\n        initialLimit\n    ]);\n    /**\n   * Cargar datos iniciales\n   */ const loadInitialData = async ()=>{\n        const startTime = Date.now();\n        try {\n            setIsLoading(true);\n            const [themesData, popularFAQs, statsData] = await Promise.all([\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getThemes(),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(initialLimit),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQStats()\n            ]);\n            setThemes(themesData);\n            setFaqs(popularFAQs);\n            setStats(statsData);\n            // Registrar carga de sección\n            const loadTime = Date.now() - startTime;\n            _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackSectionLoad(\"faq-section\", loadTime);\n        } catch (error) {\n            console.error(\"Error loading FAQ data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Realizar búsqueda de FAQs\n   */ const performSearch = async ()=>{\n        try {\n            if (searchQuery.trim()) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].searchFAQs(searchQuery, {\n                    theme: selectedTheme || undefined,\n                    limit: showAllFAQs ? 50 : initialLimit\n                });\n                setFaqs(results);\n            } else if (selectedTheme) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQsByTheme(selectedTheme, showAllFAQs ? undefined : initialLimit);\n                setFaqs(results);\n            } else {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(showAllFAQs ? 50 : initialLimit);\n                setFaqs(results);\n            }\n        } catch (error) {\n            console.error(\"Error searching FAQs:\", error);\n        }\n    };\n    /**\n   * Alternar expansión de FAQ\n   */ const toggleFAQExpansion = (faqId)=>{\n        const newExpanded = new Set(expandedFAQs);\n        if (newExpanded.has(faqId)) {\n            newExpanded.delete(faqId);\n        } else {\n            newExpanded.add(faqId);\n            // Registrar visualización cuando se expande\n            const faq = faqs.find((f)=>f.id === faqId);\n            if (faq) {\n                _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackFAQView(faq.id, faq.question);\n            }\n        }\n        setExpandedFAQs(newExpanded);\n    };\n    /**\n   * Limpiar filtros\n   */ const clearFilters = ()=>{\n        setSearchQuery(\"\");\n        setSelectedTheme(\"\");\n        setShowAllFAQs(false);\n    };\n    /**\n   * Obtener icono de categoría\n   */ const getCategoryIcon = (iconName)=>{\n        const IconComponent = categoryIcons[iconName] || _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        return IconComponent;\n    };\n    // FAQs filtrados y limitados\n    const displayedFAQs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return showAllFAQs ? faqs : faqs.slice(0, initialLimit);\n    }, [\n        faqs,\n        showAllFAQs,\n        initialLimit\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-chia-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Cargando preguntas frecuentes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-8 w-8 text-chia-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalFAQs,\n                                    \" preguntas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalThemes,\n                                    \" temas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"M\\xe1s consultado: \",\n                                    stats.mostPopularTheme\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            (showSearch || showCategoryFilter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"Buscar en preguntas frecuentes...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 19\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 17\n                            }, this),\n                            showCategoryFilter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filtrar por categor\\xeda:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: selectedCategory === \"\" ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSelectedCategory(\"\"),\n                                                className: \"h-8\",\n                                                children: \"Todas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this),\n                                            categories.map((category)=>{\n                                                const IconComponent = getCategoryIcon(category.icon);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setSelectedCategory(category.id);\n                                                        _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackCategoryFilter(category.id);\n                                                    },\n                                                    className: \"h-8 space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"ml-1 h-4 text-xs\",\n                                                            children: category.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, category.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, this),\n                            (searchQuery || selectedCategory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: clearFilters,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Limpiar filtros\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: displayedFAQs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No se encontraron preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: searchQuery ? 'No hay resultados para \"'.concat(searchQuery, '\"') : \"No hay preguntas disponibles en esta categor\\xeda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this),\n                            (searchQuery || selectedCategory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: clearFilters,\n                                children: \"Ver todas las preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        displayedFAQs.map((faq)=>{\n                            const isExpanded = expandedFAQs.has(faq.id);\n                            const category = categories.find((cat)=>cat.id === faq.category);\n                            const IconComponent = category ? getCategoryIcon(category.icon) : _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"transition-all duration-200 hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"cursor-pointer\",\n                                        onClick: ()=>toggleFAQExpansion(faq.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-4 w-4 text-chia-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: category === null || category === void 0 ? void 0 : category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        faq.popularity,\n                                                                        \"% popular\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-left text-lg leading-tight\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"ml-4 flex-shrink-0\",\n                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this),\n                                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 25\n                                                }, this),\n                                                faq.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: faq.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 27\n                                                }, this),\n                                                faq.relatedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Tr\\xe1mites relacionados:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: faq.relatedProcedures.map((procedure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-chia-blue-600\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        procedure\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 33\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, faq.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        !showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowAllFAQs(true),\n                                className: \"px-8\",\n                                children: [\n                                    \"Ver m\\xe1s preguntas (\",\n                                    faqs.length - initialLimit,\n                                    \" restantes)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 15\n                        }, this),\n                        showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                onClick: ()=>setShowAllFAQs(false),\n                                className: \"px-8\",\n                                children: \"Ver menos preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQSection, \"fsnOQ7wz/gQXT5txZJwtK7fPzB8=\");\n_c = FAQSection;\nvar _c;\n$RefreshReg$(_c, \"FAQSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/faq/FAQSection.tsx\n"));

/***/ })

});