"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/services/faqService.ts":
/*!************************************!*\
  !*** ./lib/services/faqService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n/* harmony import */ var _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n\n\n/**\n * Servicio para gestionar preguntas frecuentes municipales\n * Conectado con Supabase usando búsqueda de texto completo en español\n */ class FAQService {\n    static getInstance() {\n        if (!FAQService.instance) {\n            FAQService.instance = new FAQService();\n        }\n        return FAQService.instance;\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQItem\n   */ mapFAQFromDB(faqRow, themeName) {\n        return {\n            id: faqRow.id,\n            question: faqRow.question,\n            answer: faqRow.answer,\n            theme: themeName || \"\",\n            themeId: faqRow.theme_id,\n            keywords: faqRow.keywords || [],\n            displayOrder: faqRow.display_order,\n            popularityScore: faqRow.popularity_score || 0,\n            viewCount: faqRow.view_count || 0,\n            helpfulVotes: faqRow.helpful_votes || 0,\n            unhelpfulVotes: faqRow.unhelpful_votes || 0,\n            lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || \"\")\n        };\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQTheme\n   */ mapThemeFromDB(themeRow) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return {\n            id: themeRow.id,\n            name: themeRow.name,\n            description: themeRow.description,\n            displayOrder: themeRow.display_order,\n            dependencyId: themeRow.dependency_id,\n            subdependencyId: themeRow.subdependency_id,\n            count\n        };\n    }\n    /**\n   * Obtener todos los temas desde Supabase\n   */ async getThemes() {\n        try {\n            // Verificar cache\n            const now = Date.now();\n            if (now - this.lastCacheUpdate < this.cacheExpiry && this.themesCache.size > 0) {\n                return Array.from(this.themesCache.values()).sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n            }\n            // Obtener temas activos\n            const { data: themesData, error: themesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_themes\").select(\"*\").eq(\"is_active\", true).order(\"display_order\");\n            if (themesError) {\n                console.error(\"Error fetching FAQ themes:\", themesError);\n                return [];\n            }\n            // Obtener conteo real de FAQs por tema\n            const { data: faqCounts, error: countError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"theme_id\").eq(\"is_active\", true);\n            if (countError) {\n                console.error(\"Error fetching FAQ counts:\", countError);\n            }\n            // Crear mapa de conteos\n            const countMap = new Map();\n            faqCounts === null || faqCounts === void 0 ? void 0 : faqCounts.forEach((faq)=>{\n                if (faq.theme_id) {\n                    countMap.set(faq.theme_id, (countMap.get(faq.theme_id) || 0) + 1);\n                }\n            });\n            // Mapear y cachear temas\n            const themes = (themesData === null || themesData === void 0 ? void 0 : themesData.map((theme)=>this.mapThemeFromDB(theme, countMap.get(theme.id) || 0))) || [];\n            // Actualizar cache\n            this.themesCache.clear();\n            themes.forEach((theme)=>this.themesCache.set(theme.id, theme));\n            this.lastCacheUpdate = now;\n            return themes.sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n        } catch (error) {\n            console.error(\"Error in getThemes:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQs por categoría desde Supabase\n   */ async getFAQsByCategory(categoryId, limit) {\n        try {\n            let query = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"category_id\", categoryId).eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            });\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching FAQs by category:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getFAQsByCategory:\", error);\n            return [];\n        }\n    }\n    /**\n   * Buscar FAQs por texto usando Supabase\n   */ async searchFAQs(query) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const startTime = Date.now();\n        const { category, limit = 10, includeRelated = true } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        try {\n            const searchTerm = query.toLowerCase().trim();\n            // Construir query base\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true);\n            // Filtrar por categoría si se especifica\n            if (category) {\n                supabaseQuery = supabaseQuery.eq(\"category_id\", category);\n            }\n            // Usar búsqueda de texto completo\n            const { data, error } = await supabaseQuery.or(\"question.ilike.%\".concat(searchTerm, \"%,answer.ilike.%\").concat(searchTerm, \"%\")).order(\"popularity\", {\n                ascending: false\n            }).limit(limit * 2) // Obtener más para filtrar después\n            ;\n            if (error) {\n                console.error(\"Error searching FAQs:\", error);\n                return [];\n            }\n            // Filtrar y ordenar resultados\n            let results = (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n            // Filtrar por tags y procedimientos relacionados si includeRelated es true\n            if (includeRelated) {\n                results = results.filter((faq)=>{\n                    const questionMatch = faq.question.toLowerCase().includes(searchTerm);\n                    const answerMatch = faq.answer.toLowerCase().includes(searchTerm);\n                    const tagMatch = faq.tags.some((tag)=>tag.toLowerCase().includes(searchTerm));\n                    const procedureMatch = faq.relatedProcedures.some((proc)=>proc.toLowerCase().includes(searchTerm));\n                    return questionMatch || answerMatch || tagMatch || procedureMatch;\n                });\n            }\n            // Ordenar por relevancia\n            results.sort((a, b)=>{\n                const aScore = this.calculateRelevanceScore(a, searchTerm);\n                const bScore = this.calculateRelevanceScore(b, searchTerm);\n                return bScore - aScore;\n            });\n            const finalResults = results.slice(0, limit);\n            const responseTime = Date.now() - startTime;\n            // Registrar analytics\n            if (finalResults.length === 0) {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackNoResults(query, category);\n            } else {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackSearch(query, finalResults.length, responseTime, category);\n            }\n            return finalResults;\n        } catch (error) {\n            console.error(\"Error in searchFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Calcular puntuación de relevancia\n   */ calculateRelevanceScore(faq, searchTerm) {\n        let score = 0;\n        const term = searchTerm.toLowerCase();\n        // Coincidencia exacta en pregunta (peso alto)\n        if (faq.question.toLowerCase().includes(term)) {\n            score += 100;\n        }\n        // Coincidencia en respuesta (peso medio)\n        if (faq.answer.toLowerCase().includes(term)) {\n            score += 50;\n        }\n        // Coincidencia en tags (peso medio)\n        faq.tags.forEach((tag)=>{\n            if (tag.toLowerCase().includes(term)) {\n                score += 30;\n            }\n        });\n        // Popularidad (peso bajo)\n        score += faq.popularity * 0.1;\n        return score;\n    }\n    /**\n   * Obtener FAQs más populares desde Supabase\n   */ async getPopularFAQs() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        try {\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error fetching popular FAQs:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getPopularFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQ por ID desde Supabase\n   */ async getFAQById(id) {\n        try {\n            var _data_faq_categories;\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"id\", id).eq(\"is_active\", true).single();\n            if (error) {\n                console.error(\"Error fetching FAQ by ID:\", error);\n                return null;\n            }\n            const faq = this.mapFAQFromDB(data, (_data_faq_categories = data.faq_categories) === null || _data_faq_categories === void 0 ? void 0 : _data_faq_categories.name);\n            // Registrar visualización y actualizar contador\n            _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackFAQView(faq.id, faq.question);\n            // Incrementar view_count en la base de datos\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").update({\n                view_count: (data.view_count || 0) + 1\n            }).eq(\"id\", id);\n            return faq;\n        } catch (error) {\n            console.error(\"Error in getFAQById:\", error);\n            return null;\n        }\n    }\n    /**\n   * Obtener estadísticas del FAQ desde Supabase\n   */ async getFAQStats() {\n        try {\n            // Obtener estadísticas de FAQs\n            const { data: faqStats, error: faqError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"popularity, category_id\").eq(\"is_active\", true);\n            if (faqError) {\n                console.error(\"Error fetching FAQ stats:\", faqError);\n                return {\n                    totalFAQs: 0,\n                    totalCategories: 0,\n                    averagePopularity: 0,\n                    mostPopularCategory: \"\"\n                };\n            }\n            // Obtener estadísticas de categorías directamente\n            const { data: categoryStats, error: categoryError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"id, name\").eq(\"is_active\", true);\n            if (categoryError) {\n                console.error(\"Error fetching category stats:\", categoryError);\n            }\n            // Calcular estadísticas\n            const totalFAQs = (faqStats === null || faqStats === void 0 ? void 0 : faqStats.length) || 0;\n            const totalCategories = (categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.length) || 0;\n            const averagePopularity = totalFAQs > 0 ? Math.round(faqStats.reduce((sum, faq)=>sum + (faq.popularity || 0), 0) / totalFAQs) : 0;\n            // Calcular categoría más popular sin llamar a getCategories()\n            const categoryCount = new Map();\n            faqStats === null || faqStats === void 0 ? void 0 : faqStats.forEach((faq)=>{\n                if (faq.category_id) {\n                    categoryCount.set(faq.category_id, (categoryCount.get(faq.category_id) || 0) + 1);\n                }\n            });\n            let mostPopularCategory = \"\";\n            let maxCount = 0;\n            categoryCount.forEach((count, categoryId)=>{\n                if (count > maxCount) {\n                    maxCount = count;\n                    const category = categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.find((cat)=>cat.id === categoryId);\n                    mostPopularCategory = (category === null || category === void 0 ? void 0 : category.name) || \"\";\n                }\n            });\n            return {\n                totalFAQs,\n                totalCategories,\n                averagePopularity,\n                mostPopularCategory\n            };\n        } catch (error) {\n            console.error(\"Error in getFAQStats:\", error);\n            return {\n                totalFAQs: 0,\n                totalCategories: 0,\n                averagePopularity: 0,\n                mostPopularCategory: \"\"\n            };\n        }\n    }\n    constructor(){\n        this.themesCache = new Map();\n        this.cacheExpiry = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.lastCacheUpdate = 0;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/faqService.ts\n"));

/***/ })

});