-- Optimized Final Completion Batch 18
-- Questions 334 to 348
-- Total questions in batch: 15

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿EN QUÉ CONSISTE LA AGENCIA PUBLICA DE EMPLEO (APE)?', 'La Agencia Pública de Gestión y Colocación de Empleo de Chía, bajo la Unidad del Servicio Público de Empleo (SPE), adscrita al Ministerio del Trabajo, tiene como misión administrar y promocionar el Servicio Público de Empleo, garantizando un funcionamiento eficiente y oportuno.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['servicio', 'consiste', 'agencia', 'publica', 'empleo', 'agencia'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿QUÉ SERVICIOS OFRECE LA AGENCIA PUBLICA DE EMPLEO (APE)?', 'REGISTRO: Oferentes (buscadores de Empleo), Potenciales Empleadores y Vacantes ORIENTACIÓN OCUPACIONAL: Asesoría para oferentes y empleadores en la búsqueda de empleo y creación de vacantes PRESELECCIÓN: Identificación de candidatos que cumplen con el perfil solicitado por las empresas REMISIÓN: Envió de las hojas de vida preseleccionadas a las empresas', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['registro', 'servicio', 'servicios', 'ofrece', 'agencia', 'publica', 'empleo'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿A QUIÉN LE PRESTA SERVICOS LA AGENCIA PUBLICA DE EMPLEO (APE)??', '1. PERSONAS (OFERENTES - BUSCADORES DE EMPLEO) 2. EMPRESAS (DEMANDANTES)', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['quién', 'presta', 'servicos', 'agencia', 'publica'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿QUÉ SERVICIOS OFRECE LA AGENCIA PUBLICA DE EMPLEO (APE) A LOS OFERENTES O BUSCADORES DE EMPLEO?', 'El buscador de empleo puede acceder de manera gratuita a los siguientes servicios en la Ruta Única de Empleabilidad: 1. REGISTRO: Inscripción de hoja de vida de forma presencial o virtual, ya sea de manera autónoma en el enlace https://personas.serviciodeempleo.gov.co o asistida dirigiéndose al prestador del SPE en su territorio. 2. ORIENTACIÓN OCUPACIONAL: En la orientación ocupacional estará acompañado de un profesional especializado que le brindará asesoría en la construcción de la hoja de vida y el perfil ocupacional, le brindará información del mercado laboral y de estrategias para mejorar la búsqueda de empleo.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['registro', 'presencial', 'virtual', 'servicio', 'servicios', 'ofrece', 'agencia', 'publica'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿QUÉ SERVICIOS OFRECE AGENCIA PUBLICA DE EMPLEO (APE) A LAS EMPRESAS (DEMANDANTES)?', '1. REGISTRO: Inscripción de la Empresa en la plataforma SISE, para lo cual se requiere la documentación pertinente 2. PRESELECCIÓN: Identificación de candidatos que cumplan con el perfil solicitado por la empresa. 4. REMISIÓN: Envío de las hojas de vida de los candidatos preseleccionados', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['registro', 'servicio', 'servicios', 'ofrece', 'agencia', 'publica', 'empleo'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿QUE REQUISITOS SE NECESITAN PARA UNA ALIANZA CON UNA EMPRESA?', 'Auto registro virtual a través del enlace http://empresas.serviciodeempleo.gov.co/HomeEmpresa.aspx. Es necesario adjuntar la Cámara de Comercio, RUT, fotocopia de la cédula del representante legal y datos de la persona encargada del proceso. Registro asistido enviando los documentos <NAME_EMAIL>.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['requisito', 'documento', 'registro', 'virtual', 'servicio', 'requisitos', 'necesitan', 'para'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿COMO INSCRIBIR LA HOJA DE VIDA EN LA PLATAFORMA SISE?', 'Para la búsqueda de empleo es fundamental diligenciar el 100% la hoja de vida en la plataforma y mantener la información actualizada. Al realizar el registro en el portal, se da la opción de crear de forma fácil y segura la hoja de vida, con la cual se puede aplicar a las ofertas de empleo vigentes de acuerdo con el perfil que quedó registrado, así las mejores empresas del país podrán conocer la información. Para acceder a la plataforma del Sistema de Información de Servicio de Empleo (SISE) y realizar el registro de la hoja de vida de forma autónoma (auto registro), es necesario seguir los siguientes pasos: Ubicar la sección Buscadores de Empleo. También es posible ingresar directamente con el link https://personas.serviciodeempleo.gov.co/login.aspx, en donde hay dos opciones disponibles: 1. Si anteriormente se había hecho el registro, solo hay que digitar el tipo de documento, número y contraseña para ingresar. 2. Si es la primera vez en ingresar al sistema, primero hay que crear una cuenta dando clic en el botón “Regístrese”. En esta sección se debe inscribir la cuenta de correo electrónico para confirmar si la hoja de vida ya se encuentra registrada;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿TIENE COSTO EL REGISTRO DE LA HOJA DE VIDA?', 'NO, es un servicio gratuito', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['registro', 'costo', 'gratuito', 'servicio', 'tiene', 'hoja', 'vida'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿LA INSCRIPCION DE LA HOJA DE VIDA SE PUEDE HACER EN LINEA?', 'SI, a través del link https://personas.serviciodeempleo.gov.co/login.aspx', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['inscripcion', 'linea', 'servicio', 'hoja', 'vida', 'puede', 'hacer'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿LA INSCRIPCION DE LA HOJA DE VIDA SE PUEDE HACER PRESENCIAL?', 'SI, en la sede ubicada en la CARRERA 10 # 8 - 72 Centro Comercial El Curubito, Chía- Cundinamarca', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['inscripcion', 'presencial', 'hoja', 'vida', 'puede', 'hacer'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿QUE VACANTES TIENEN?', 'Bachilleres Técnicos Profesionales Administrativos Operativos Auxiliares', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['vacantes', 'tienen', 'bachilleres', 'técnicos', 'profesionales'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿QUÉ FERIAS MANEJA LA AGENCIA DE EMPLEO?', ' Ferias de empleabilidad todos los martes de 9:00 a.m. a 12:00 p.m. en la CARRERA 10 # 8 - 72 Centro Comercial El Curubito, Chía- Cundinamarca  Ferias descentralizadas el último martes de cada mes en las veredas del municipio de Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['ferias', 'maneja', 'agencia', 'empleo', 'ferias'], 12);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿CUÁLES SON LOS HORARIOS DE ATENCIÓN DE LA AGENCIA PÚBLICA DE EMPLEO?', 'De lunes a viernes, de 9:00 am 4:00 pm Jornada continua', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['horario', 'cuáles', 'horarios', 'atención', 'agencia', 'pública'], 13);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿CUALES SON LOS CANALES DE ATENCION DE LA AGENCIA PÚBLICA DE EMPLEO?', 'PRESENCIAL: CARRERA 10 # 8 - 72 Centro Comercial El Curubito, Chía- Cundinamarca VIRTUAL: correo <EMAIL> WHATSAAP: 3164972050 TELEFÓNICO: 60188444444 extensión 1302', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['presencial', 'virtual', 'cuales', 'canales', 'atencion', 'agencia', 'pública'], 14);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿EXISTE OFERTAS DE EMPLEO PARA PERSONA CON DISCAPACIDAD?', 'SI, contamos con ofertas para personas con discapacidad certificada y un equipo especializado para brindarles atención.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['existe', 'ofertas', 'empleo', 'para', 'persona'], 15);
