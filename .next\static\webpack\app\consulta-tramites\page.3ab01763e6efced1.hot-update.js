"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/consulta-tramites/page",{

/***/ "(app-pages-browser)/./lib/services/faqService.ts":
/*!************************************!*\
  !*** ./lib/services/faqService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n/* harmony import */ var _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n\n\n/**\n * Servicio para gestionar preguntas frecuentes\n * Ahora conectado con Supabase para datos dinámicos\n */ class FAQService {\n    static getInstance() {\n        if (!FAQService.instance) {\n            FAQService.instance = new FAQService();\n        }\n        return FAQService.instance;\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQItem\n   */ mapFAQFromDB(faqRow, categoryName) {\n        return {\n            id: faqRow.id,\n            question: faqRow.question,\n            answer: faqRow.answer,\n            category: categoryName || \"\",\n            categoryId: faqRow.category_id,\n            tags: faqRow.tags || [],\n            relatedProcedures: faqRow.related_procedures || [],\n            popularity: faqRow.popularity || 0,\n            viewCount: faqRow.view_count || 0,\n            helpfulVotes: faqRow.helpful_votes || 0,\n            unhelpfulVotes: faqRow.unhelpful_votes || 0,\n            lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || \"\")\n        };\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQCategory\n   */ mapCategoryFromDB(categoryRow) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return {\n            id: categoryRow.id,\n            name: categoryRow.name,\n            description: categoryRow.description,\n            icon: categoryRow.icon,\n            color: categoryRow.color,\n            displayOrder: categoryRow.display_order,\n            count\n        };\n    }\n    /**\n   * Obtener todas las categorías desde Supabase\n   */ async getCategories() {\n        try {\n            // Verificar cache\n            const now = Date.now();\n            if (now - this.lastCacheUpdate < this.cacheExpiry && this.categoriesCache.size > 0) {\n                return Array.from(this.categoriesCache.values()).sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n            }\n            // Obtener categorías con conteo de FAQs\n            const { data: categoriesData, error: categoriesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"\\n          *,\\n          faqs!inner(count)\\n        \").eq(\"is_active\", true).order(\"display_order\");\n            if (categoriesError) {\n                console.error(\"Error fetching FAQ categories:\", categoriesError);\n                return [];\n            }\n            // Obtener conteo real de FAQs por categoría\n            const { data: faqCounts, error: countError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"category_id\").eq(\"is_active\", true);\n            if (countError) {\n                console.error(\"Error fetching FAQ counts:\", countError);\n            }\n            // Crear mapa de conteos\n            const countMap = new Map();\n            faqCounts === null || faqCounts === void 0 ? void 0 : faqCounts.forEach((faq)=>{\n                if (faq.category_id) {\n                    countMap.set(faq.category_id, (countMap.get(faq.category_id) || 0) + 1);\n                }\n            });\n            // Mapear y cachear categorías\n            const categories = (categoriesData === null || categoriesData === void 0 ? void 0 : categoriesData.map((cat)=>this.mapCategoryFromDB(cat, countMap.get(cat.id) || 0))) || [];\n            // Actualizar cache\n            this.categoriesCache.clear();\n            categories.forEach((cat)=>this.categoriesCache.set(cat.id, cat));\n            this.lastCacheUpdate = now;\n            return categories.sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n        } catch (error) {\n            console.error(\"Error in getCategories:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQs por categoría desde Supabase\n   */ async getFAQsByCategory(categoryId, limit) {\n        try {\n            let query = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"category_id\", categoryId).eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            });\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching FAQs by category:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getFAQsByCategory:\", error);\n            return [];\n        }\n    }\n    /**\n   * Buscar FAQs por texto usando Supabase\n   */ async searchFAQs(query) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const startTime = Date.now();\n        const { category, limit = 10, includeRelated = true } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        try {\n            const searchTerm = query.toLowerCase().trim();\n            // Construir query base\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true);\n            // Filtrar por categoría si se especifica\n            if (category) {\n                supabaseQuery = supabaseQuery.eq(\"category_id\", category);\n            }\n            // Usar búsqueda de texto completo\n            const { data, error } = await supabaseQuery.or(\"question.ilike.%\".concat(searchTerm, \"%,answer.ilike.%\").concat(searchTerm, \"%\")).order(\"popularity\", {\n                ascending: false\n            }).limit(limit * 2) // Obtener más para filtrar después\n            ;\n            if (error) {\n                console.error(\"Error searching FAQs:\", error);\n                return [];\n            }\n            // Filtrar y ordenar resultados\n            let results = (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n            // Filtrar por tags y procedimientos relacionados si includeRelated es true\n            if (includeRelated) {\n                results = results.filter((faq)=>{\n                    const questionMatch = faq.question.toLowerCase().includes(searchTerm);\n                    const answerMatch = faq.answer.toLowerCase().includes(searchTerm);\n                    const tagMatch = faq.tags.some((tag)=>tag.toLowerCase().includes(searchTerm));\n                    const procedureMatch = faq.relatedProcedures.some((proc)=>proc.toLowerCase().includes(searchTerm));\n                    return questionMatch || answerMatch || tagMatch || procedureMatch;\n                });\n            }\n            // Ordenar por relevancia\n            results.sort((a, b)=>{\n                const aScore = this.calculateRelevanceScore(a, searchTerm);\n                const bScore = this.calculateRelevanceScore(b, searchTerm);\n                return bScore - aScore;\n            });\n            const finalResults = results.slice(0, limit);\n            const responseTime = Date.now() - startTime;\n            // Registrar analytics\n            if (finalResults.length === 0) {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackNoResults(query, category);\n            } else {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackSearch(query, finalResults.length, responseTime, category);\n            }\n            return finalResults;\n        } catch (error) {\n            console.error(\"Error in searchFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Calcular puntuación de relevancia\n   */ calculateRelevanceScore(faq, searchTerm) {\n        let score = 0;\n        const term = searchTerm.toLowerCase();\n        // Coincidencia exacta en pregunta (peso alto)\n        if (faq.question.toLowerCase().includes(term)) {\n            score += 100;\n        }\n        // Coincidencia en respuesta (peso medio)\n        if (faq.answer.toLowerCase().includes(term)) {\n            score += 50;\n        }\n        // Coincidencia en tags (peso medio)\n        faq.tags.forEach((tag)=>{\n            if (tag.toLowerCase().includes(term)) {\n                score += 30;\n            }\n        });\n        // Popularidad (peso bajo)\n        score += faq.popularity * 0.1;\n        return score;\n    }\n    /**\n   * Obtener FAQs más populares desde Supabase\n   */ async getPopularFAQs() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        try {\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error fetching popular FAQs:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getPopularFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQ por ID desde Supabase\n   */ async getFAQById(id) {\n        try {\n            var _data_faq_categories;\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"id\", id).eq(\"is_active\", true).single();\n            if (error) {\n                console.error(\"Error fetching FAQ by ID:\", error);\n                return null;\n            }\n            const faq = this.mapFAQFromDB(data, (_data_faq_categories = data.faq_categories) === null || _data_faq_categories === void 0 ? void 0 : _data_faq_categories.name);\n            // Registrar visualización y actualizar contador\n            _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackFAQView(faq.id, faq.question);\n            // Incrementar view_count en la base de datos\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").update({\n                view_count: (data.view_count || 0) + 1\n            }).eq(\"id\", id);\n            return faq;\n        } catch (error) {\n            console.error(\"Error in getFAQById:\", error);\n            return null;\n        }\n    }\n    /**\n   * Obtener estadísticas del FAQ desde Supabase\n   */ async getFAQStats() {\n        try {\n            // Obtener estadísticas de FAQs\n            const { data: faqStats, error: faqError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"popularity, category_id\").eq(\"is_active\", true);\n            if (faqError) {\n                console.error(\"Error fetching FAQ stats:\", faqError);\n                return {\n                    totalFAQs: 0,\n                    totalCategories: 0,\n                    averagePopularity: 0,\n                    mostPopularCategory: \"\"\n                };\n            }\n            // Obtener estadísticas de categorías directamente\n            const { data: categoryStats, error: categoryError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"id, name\").eq(\"is_active\", true);\n            if (categoryError) {\n                console.error(\"Error fetching category stats:\", categoryError);\n            }\n            // Calcular estadísticas\n            const totalFAQs = (faqStats === null || faqStats === void 0 ? void 0 : faqStats.length) || 0;\n            const totalCategories = (categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.length) || 0;\n            const averagePopularity = totalFAQs > 0 ? Math.round(faqStats.reduce((sum, faq)=>sum + (faq.popularity || 0), 0) / totalFAQs) : 0;\n            // Calcular categoría más popular sin llamar a getCategories()\n            const categoryCount = new Map();\n            faqStats === null || faqStats === void 0 ? void 0 : faqStats.forEach((faq)=>{\n                if (faq.category_id) {\n                    categoryCount.set(faq.category_id, (categoryCount.get(faq.category_id) || 0) + 1);\n                }\n            });\n            let mostPopularCategory = \"\";\n            let maxCount = 0;\n            categoryCount.forEach((count, categoryId)=>{\n                if (count > maxCount) {\n                    maxCount = count;\n                    const category = categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.find((cat)=>cat.id === categoryId);\n                    mostPopularCategory = (category === null || category === void 0 ? void 0 : category.name) || \"\";\n                }\n            });\n            return {\n                totalFAQs,\n                totalCategories,\n                averagePopularity,\n                mostPopularCategory\n            };\n        } catch (error) {\n            console.error(\"Error in getFAQStats:\", error);\n            return {\n                totalFAQs: 0,\n                totalCategories: 0,\n                averagePopularity: 0,\n                mostPopularCategory: \"\"\n            };\n        }\n    }\n    constructor(){\n        this.categoriesCache = new Map();\n        this.cacheExpiry = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.lastCacheUpdate = 0;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/faqService.ts\n"));

/***/ })

});