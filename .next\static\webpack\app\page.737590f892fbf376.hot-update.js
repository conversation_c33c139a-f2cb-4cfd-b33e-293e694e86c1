"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/services/faqService.ts":
/*!************************************!*\
  !*** ./lib/services/faqService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n/* harmony import */ var _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n\n\n/**\n * Servicio para gestionar preguntas frecuentes municipales\n * Conectado con Supabase usando búsqueda de texto completo en español\n */ class FAQService {\n    static getInstance() {\n        if (!FAQService.instance) {\n            FAQService.instance = new FAQService();\n        }\n        return FAQService.instance;\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQItem\n   */ mapFAQFromDB(faqRow, themeName) {\n        return {\n            id: faqRow.id,\n            question: faqRow.question,\n            answer: faqRow.answer,\n            theme: themeName || \"\",\n            themeId: faqRow.theme_id,\n            keywords: faqRow.keywords || [],\n            displayOrder: faqRow.display_order,\n            popularityScore: faqRow.popularity_score || 0,\n            viewCount: faqRow.view_count || 0,\n            helpfulVotes: faqRow.helpful_votes || 0,\n            unhelpfulVotes: faqRow.unhelpful_votes || 0,\n            lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || \"\")\n        };\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQTheme\n   */ mapThemeFromDB(themeRow) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return {\n            id: themeRow.id,\n            name: themeRow.name,\n            description: themeRow.description,\n            displayOrder: themeRow.display_order,\n            dependencyId: themeRow.dependency_id,\n            subdependencyId: themeRow.subdependency_id,\n            count\n        };\n    }\n    /**\n   * Obtener todos los temas desde Supabase\n   */ async getThemes() {\n        try {\n            // Verificar cache\n            const now = Date.now();\n            if (now - this.lastCacheUpdate < this.cacheExpiry && this.themesCache.size > 0) {\n                return Array.from(this.themesCache.values()).sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n            }\n            // Obtener temas activos\n            const { data: themesData, error: themesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_themes\").select(\"*\").eq(\"is_active\", true).order(\"display_order\");\n            if (themesError) {\n                console.error(\"Error fetching FAQ themes:\", themesError);\n                return [];\n            }\n            // Obtener conteo real de FAQs por tema\n            const { data: faqCounts, error: countError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"theme_id\").eq(\"is_active\", true);\n            if (countError) {\n                console.error(\"Error fetching FAQ counts:\", countError);\n            }\n            // Crear mapa de conteos\n            const countMap = new Map();\n            faqCounts === null || faqCounts === void 0 ? void 0 : faqCounts.forEach((faq)=>{\n                if (faq.theme_id) {\n                    countMap.set(faq.theme_id, (countMap.get(faq.theme_id) || 0) + 1);\n                }\n            });\n            // Mapear y cachear temas\n            const themes = (themesData === null || themesData === void 0 ? void 0 : themesData.map((theme)=>this.mapThemeFromDB(theme, countMap.get(theme.id) || 0))) || [];\n            // Actualizar cache\n            this.themesCache.clear();\n            themes.forEach((theme)=>this.themesCache.set(theme.id, theme));\n            this.lastCacheUpdate = now;\n            return themes.sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n        } catch (error) {\n            console.error(\"Error in getThemes:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQs por tema desde Supabase\n   */ async getFAQsByTheme(themeId, limit) {\n        try {\n            let query = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"\\n          *,\\n          faq_themes!inner(name)\\n        \").eq(\"theme_id\", themeId).eq(\"is_active\", true).order(\"popularity_score\", {\n                ascending: false\n            });\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching FAQs by theme:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_themes;\n                return this.mapFAQFromDB(faq, (_faq_faq_themes = faq.faq_themes) === null || _faq_faq_themes === void 0 ? void 0 : _faq_faq_themes.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getFAQsByTheme:\", error);\n            return [];\n        }\n    }\n    /**\n   * Buscar FAQs usando búsqueda de texto completo en español\n   */ async searchFAQs(query) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const startTime = Date.now();\n        const { theme, dependencyId, subdependencyId, limit = 10, includeKeywords = true } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        try {\n            const searchTerm = query.trim();\n            // Construir query base con búsqueda de texto completo\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"\\n          *,\\n          faq_themes!inner(name, dependency_id, subdependency_id)\\n        \").eq(\"is_active\", true);\n            // Filtrar por tema si se especifica\n            if (theme) {\n                supabaseQuery = supabaseQuery.eq(\"theme_id\", theme);\n            }\n            // Filtrar por dependencia si se especifica\n            if (dependencyId) {\n                supabaseQuery = supabaseQuery.eq(\"faq_themes.dependency_id\", dependencyId);\n            }\n            // Filtrar por subdependencia si se especifica\n            if (subdependencyId) {\n                supabaseQuery = supabaseQuery.eq(\"faq_themes.subdependency_id\", subdependencyId);\n            }\n            // Usar búsqueda de texto completo en español con tsvector\n            const { data, error } = await supabaseQuery.textSearch(\"search_vector\", \"'\".concat(searchTerm, \"'\"), {\n                type: \"plainto\",\n                config: \"spanish\"\n            }).order(\"popularity_score\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error searching FAQs:\", error);\n                // Fallback a búsqueda simple si falla la búsqueda de texto completo\n                return this.fallbackSearch(searchTerm, options);\n            }\n            // Mapear resultados\n            const results = (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_themes;\n                return this.mapFAQFromDB(faq, (_faq_faq_themes = faq.faq_themes) === null || _faq_faq_themes === void 0 ? void 0 : _faq_faq_themes.name);\n            })) || [];\n            const responseTime = Date.now() - startTime;\n            // Registrar analytics\n            if (results.length === 0) {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackNoResults(query, theme);\n            } else {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackSearch(query, results.length, responseTime, theme);\n            }\n            return results;\n        } catch (error) {\n            console.error(\"Error in searchFAQs:\", error);\n            return this.fallbackSearch(query, options);\n        }\n    }\n    /**\n   * Búsqueda de respaldo usando ILIKE\n   */ async fallbackSearch(query, options) {\n        try {\n            const { theme, limit = 10 } = options;\n            const searchTerm = query.toLowerCase().trim();\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"\\n          *,\\n          faq_themes!inner(name)\\n        \").eq(\"is_active\", true);\n            if (theme) {\n                supabaseQuery = supabaseQuery.eq(\"theme_id\", theme);\n            }\n            const { data, error } = await supabaseQuery.or(\"question.ilike.%\".concat(searchTerm, \"%,answer.ilike.%\").concat(searchTerm, \"%\")).order(\"popularity_score\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error in fallback search:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_themes;\n                return this.mapFAQFromDB(faq, (_faq_faq_themes = faq.faq_themes) === null || _faq_faq_themes === void 0 ? void 0 : _faq_faq_themes.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in fallbackSearch:\", error);\n            return [];\n        }\n    }\n    /**\n   * Calcular puntuación de relevancia\n   */ calculateRelevanceScore(faq, searchTerm) {\n        let score = 0;\n        const term = searchTerm.toLowerCase();\n        // Coincidencia exacta en pregunta (peso alto)\n        if (faq.question.toLowerCase().includes(term)) {\n            score += 100;\n        }\n        // Coincidencia en respuesta (peso medio)\n        if (faq.answer.toLowerCase().includes(term)) {\n            score += 50;\n        }\n        // Coincidencia en tags (peso medio)\n        faq.tags.forEach((tag)=>{\n            if (tag.toLowerCase().includes(term)) {\n                score += 30;\n            }\n        });\n        // Popularidad (peso bajo)\n        score += faq.popularity * 0.1;\n        return score;\n    }\n    /**\n   * Obtener FAQs más populares desde Supabase\n   */ async getPopularFAQs() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        try {\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error fetching popular FAQs:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getPopularFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQ por ID desde Supabase\n   */ async getFAQById(id) {\n        try {\n            var _data_faq_categories;\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"id\", id).eq(\"is_active\", true).single();\n            if (error) {\n                console.error(\"Error fetching FAQ by ID:\", error);\n                return null;\n            }\n            const faq = this.mapFAQFromDB(data, (_data_faq_categories = data.faq_categories) === null || _data_faq_categories === void 0 ? void 0 : _data_faq_categories.name);\n            // Registrar visualización y actualizar contador\n            _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackFAQView(faq.id, faq.question);\n            // Incrementar view_count en la base de datos\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").update({\n                view_count: (data.view_count || 0) + 1\n            }).eq(\"id\", id);\n            return faq;\n        } catch (error) {\n            console.error(\"Error in getFAQById:\", error);\n            return null;\n        }\n    }\n    /**\n   * Obtener estadísticas del FAQ desde Supabase\n   */ async getFAQStats() {\n        try {\n            // Obtener estadísticas de FAQs\n            const { data: faqStats, error: faqError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"popularity, category_id\").eq(\"is_active\", true);\n            if (faqError) {\n                console.error(\"Error fetching FAQ stats:\", faqError);\n                return {\n                    totalFAQs: 0,\n                    totalCategories: 0,\n                    averagePopularity: 0,\n                    mostPopularCategory: \"\"\n                };\n            }\n            // Obtener estadísticas de categorías directamente\n            const { data: categoryStats, error: categoryError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"id, name\").eq(\"is_active\", true);\n            if (categoryError) {\n                console.error(\"Error fetching category stats:\", categoryError);\n            }\n            // Calcular estadísticas\n            const totalFAQs = (faqStats === null || faqStats === void 0 ? void 0 : faqStats.length) || 0;\n            const totalCategories = (categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.length) || 0;\n            const averagePopularity = totalFAQs > 0 ? Math.round(faqStats.reduce((sum, faq)=>sum + (faq.popularity || 0), 0) / totalFAQs) : 0;\n            // Calcular categoría más popular sin llamar a getCategories()\n            const categoryCount = new Map();\n            faqStats === null || faqStats === void 0 ? void 0 : faqStats.forEach((faq)=>{\n                if (faq.category_id) {\n                    categoryCount.set(faq.category_id, (categoryCount.get(faq.category_id) || 0) + 1);\n                }\n            });\n            let mostPopularCategory = \"\";\n            let maxCount = 0;\n            categoryCount.forEach((count, categoryId)=>{\n                if (count > maxCount) {\n                    maxCount = count;\n                    const category = categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.find((cat)=>cat.id === categoryId);\n                    mostPopularCategory = (category === null || category === void 0 ? void 0 : category.name) || \"\";\n                }\n            });\n            return {\n                totalFAQs,\n                totalCategories,\n                averagePopularity,\n                mostPopularCategory\n            };\n        } catch (error) {\n            console.error(\"Error in getFAQStats:\", error);\n            return {\n                totalFAQs: 0,\n                totalCategories: 0,\n                averagePopularity: 0,\n                mostPopularCategory: \"\"\n            };\n        }\n    }\n    constructor(){\n        this.themesCache = new Map();\n        this.cacheExpiry = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.lastCacheUpdate = 0;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/faqService.ts\n"));

/***/ })

});