-- FAQ Chunk 12
-- Questions 221 to 240

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'Si', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Si, en los puntos de atención PACO PREGUNTAS FRECUENTES Las siguientes preguntas frecuentes se encuentran en la página web de la Alcaldía Municipal de Chía y están actualizadas para la presente vigencia 1. ¿Cuál es la normatividad tributaria vigente para el Municipio de Chía? La normatividad vigente y aplicable para los tributos en el municipio de Chía es el Acuerdo Municipal 107 de 2016 “Por medio del cual se expide el Estatuto de Rentas del municipio de Chía y se dictan otras disposiciones”, modificado por los Acuerdos Municipales 182 de 2020 y 208 de 2022, y el Decreto Municipal 069 de 2016 el cual incorpora el procedimiento tributario. Están normas se pueden consultar ingresando a la página web de la Alcaldía de Chía pestaña: trámites y servicios – impuesto de industria y comercio en “Normatividad” http://lyric-chia.seygobservices.com:3000/ 2. ¿Existe calendario tributario para la vigencia 2024? El calendario tributario del municipio de Chía para cada vigencia, se establece anualmente por medio de acto administrativo, para la vigencia 2024 está contenido en la Resolución Municipal 5389 de 2023 modificada por la Resolución Municipal 0022 de 9 de enero de 2024, las cuales se pueden consultar ingresando a la página web de la Alcaldía de Chía – transparencia- normatividad- 2.4 Normativa Alcaldía Municipal de Chía- Resoluciones;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las fechas para reclamar el recibo de impuesto predial?', 'Los recibos del impuesto predial se pueden reclamar después de la última semana del mes de enero, esto toda vez que se debe esperar que el IGAC envíe las bases de datos actualizadas con los avalúos y facturación.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['cuáles', 'fechas', 'para', 'reclamar', 'recibo'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede expedir el recibo de impuesto predial de manera virtual?', 'Si, cada contribuyente puede ingresar, descargar y pagar por PSE por la página de la alcaldía, trámites y servicios – impuesto predial – ingresan con su usuario y contraseña y descargan su factura.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['virtual', 'servicio', 'puede', 'expedir', 'recibo', 'impuesto', 'predial'], 12);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Puedo pedir un descuento de intereses si pago el total adeudado de impuesto predial?', 'Todo descuento, alivió debe ser aprobado mediante Acuerdo Municipal, aprobado por el Consejo, de no ser así, o estar establecido, no se puede hacer descuentos o acceder a las solicitudes de los contribuyentes.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['solicitud', 'puedo', 'pedir', 'descuento', 'intereses', 'pago'], 13);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué hay de cierto que los mayores de 60 años no pagan impuesto predial?', 'Es información incorrecta, no existe regulación normativa al respecto que avale tal situación, todos sin excepción deben pagar sus impuestos.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['cierto', 'mayores', 'años', 'pagan', 'impuesto'], 14);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué personas están exoneradas de pagar impuesto predial?', 'No hay nadie exento del pago de impuesto predial', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['personas', 'están', 'exoneradas', 'pagar', 'impuesto'], 15);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Puedo ceder una parte de mi predio al municipio para cubrir el pago de mi impuesto predial?', 'No, no se puede ceder parte o un predio al municipio para suplir la obligación del impuesto predial', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['puedo', 'ceder', 'parte', 'predio', 'municipio'], 16);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Colombia Mayor?', 'El Programa de Protección Social al Adulto Mayor – “Colombia Mayor” es un programa del orden Nacional que busca aumentar la protección a los adultos mayores por medio de la entrega de un subsidio económico para aquellos que se encuentran desamparados, que no cuentan con una pensión, o viven en la extrema pobreza. Requisitos para ingresar al programa Colombia mayor Dirigirse a la Dirección de Acción social Entregar los siguientes requisitos para verificación por parte de acción social Puntaje de Sisbén A, B hasta C1. No tener casa (esta información se verifica en RUAF). No tener pensión EPS subsidiada, en caso de no serlo se verificar el ingreso base de cotización. Mujeres se puede inscribir desde los 54 años y los hombres desde los 59 años. Tienen mayor prioridad personas mayores de 80 años, víctimas del conflicto armado y personas con discapacidad certificada por la Secretaría de Salud Municipal. La asignación de cupo depende del departamento para la prosperidad social del orden Nacional.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['requisito', 'programa', 'consiste', 'colombia', 'mayor'], 17);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Colombia Mayor?', 'Se realiza la inscripción en la Dirección de Acción social.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'colombia'], 18);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo el programa Colombia Mayor?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'colombia', 'mayor'], 19);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Colombia Mayor se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['programa', 'inscripción', 'colombia', 'mayor', 'puede'], 20);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede realizar la inscripción de forma presencial al programa Colombia Mayor?', 'Si, en la CRA 7 NO. 12-100. TEMA :PROGRAMA RENTA CIUDADANA', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'realizar', 'inscripción', 'forma'], 21);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Renta Ciudadana?', 'Renta Ciudadana Es un programa del DPS del Orden Nacional que busca contribuir a la superación de la pobreza, fomentar la movilidad social y apoyar la economía local, mediante la entrega de Transferencias Monetarias Condicionadas y No Condicionadas, siguiendo los principios de integralidad, efectividad y eficiencia. Su implementación se llevará a cabo de manera gradual y progresiva a través de diversas líneas de intervención. La focalización la realiza directamente el DPS y allega a la Dirección de Acción social una base de datos con las familias que han sido focalizadas. Requisitos para ingresar al programa Renta Ciudadana Población Sisbén e Indígena Sisbén de A a B4, si es población indígena se valida el certificado SIIC que expide el ministerio del Interior. Ser colombiano Esperar comunicado por parte del enlace Municipal Acercarse de manera personal', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['requisito', 'certificado', 'programa', 'consiste', 'renta', 'ciudadana', 'renta'], 22);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Renta Ciudadana Población Sisbén e Indígena?', 'No se realiza inscripción, pues el DPS selecciona a las familias beneficiarias y comunica a la Dirección de Acción Social.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'renta'], 23);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Renta Ciudadana Población Sisbén e Indígena?', 'No.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'renta'], 24);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción la inscripción al programa Renta Ciudadana Población Sisbén e Indígena se puede hacer en línea?', 'No.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['programa', 'inscripción', 'inscripción', 'renta', 'ciudadana'], 25);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer la inscripción al programa Renta Ciudadana Población Sisbén e Indígena de forma presencial?', 'No, el gobierno realiza la priorización desde las bases de datos. TEMA : PROGRAMAHABITANTE DE CALLE', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción', 'renta'], 26);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa habitante de Calle?', 'Es una estrategia que busca reducir los índices de habitabilidad para las personas que hacen de la calle su lugar de residencia permanente o transitoria, o de manera intermitente residen en diversos tipos de vivienda que combina la vivienda inadecuada, insegura y la calle, que hasta el momento se ha señalado bajo la denominación "personas en situación de calle", como la manera más clara de establecer el universo de personas hacia las cuales se deben adelantar acciones de inclusión e integración social debida a su extrema situación de exclusión socio habitacional. Requisitos para ingresar al programa habitante de Calle Dirigirse al centro transitorio de protección para que activen ruta de atención (Verificar procedencia, afiliación a Salud, Sisbén, ubicar red de apoyo, activar triage…)', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['requisito', 'programa', 'apoyo', 'consiste', 'habitante', 'calle', 'estrategia'], 27);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa habitante de Calle?', 'No aplica, se realiza en Centro Transitorio de Protección Municipal', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'habitante'], 28);
