"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/services/faqService.ts":
/*!************************************!*\
  !*** ./lib/services/faqService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n/* harmony import */ var _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n\n\n/**\n * Servicio para gestionar preguntas frecuentes municipales\n * Conectado con Supabase usando búsqueda de texto completo en español\n */ class FAQService {\n    static getInstance() {\n        if (!FAQService.instance) {\n            FAQService.instance = new FAQService();\n        }\n        return FAQService.instance;\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQItem\n   */ mapFAQFromDB(faqRow, themeName) {\n        return {\n            id: faqRow.id,\n            question: faqRow.question,\n            answer: faqRow.answer,\n            theme: themeName || \"\",\n            themeId: faqRow.theme_id,\n            keywords: faqRow.keywords || [],\n            displayOrder: faqRow.display_order,\n            popularityScore: faqRow.popularity_score || 0,\n            viewCount: faqRow.view_count || 0,\n            helpfulVotes: faqRow.helpful_votes || 0,\n            unhelpfulVotes: faqRow.unhelpful_votes || 0,\n            lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || \"\")\n        };\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQTheme\n   */ mapThemeFromDB(themeRow) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return {\n            id: themeRow.id,\n            name: themeRow.name,\n            description: themeRow.description,\n            displayOrder: themeRow.display_order,\n            dependencyId: themeRow.dependency_id,\n            subdependencyId: themeRow.subdependency_id,\n            count\n        };\n    }\n    /**\n   * Obtener todas las categorías desde Supabase\n   */ async getCategories() {\n        try {\n            // Verificar cache\n            const now = Date.now();\n            if (now - this.lastCacheUpdate < this.cacheExpiry && this.categoriesCache.size > 0) {\n                return Array.from(this.categoriesCache.values()).sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n            }\n            // Obtener categorías con conteo de FAQs\n            const { data: categoriesData, error: categoriesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"\\n          *,\\n          faqs!inner(count)\\n        \").eq(\"is_active\", true).order(\"display_order\");\n            if (categoriesError) {\n                console.error(\"Error fetching FAQ categories:\", categoriesError);\n                return [];\n            }\n            // Obtener conteo real de FAQs por categoría\n            const { data: faqCounts, error: countError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"category_id\").eq(\"is_active\", true);\n            if (countError) {\n                console.error(\"Error fetching FAQ counts:\", countError);\n            }\n            // Crear mapa de conteos\n            const countMap = new Map();\n            faqCounts === null || faqCounts === void 0 ? void 0 : faqCounts.forEach((faq)=>{\n                if (faq.category_id) {\n                    countMap.set(faq.category_id, (countMap.get(faq.category_id) || 0) + 1);\n                }\n            });\n            // Mapear y cachear categorías\n            const categories = (categoriesData === null || categoriesData === void 0 ? void 0 : categoriesData.map((cat)=>this.mapCategoryFromDB(cat, countMap.get(cat.id) || 0))) || [];\n            // Actualizar cache\n            this.categoriesCache.clear();\n            categories.forEach((cat)=>this.categoriesCache.set(cat.id, cat));\n            this.lastCacheUpdate = now;\n            return categories.sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n        } catch (error) {\n            console.error(\"Error in getCategories:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQs por categoría desde Supabase\n   */ async getFAQsByCategory(categoryId, limit) {\n        try {\n            let query = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"category_id\", categoryId).eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            });\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching FAQs by category:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getFAQsByCategory:\", error);\n            return [];\n        }\n    }\n    /**\n   * Buscar FAQs por texto usando Supabase\n   */ async searchFAQs(query) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const startTime = Date.now();\n        const { category, limit = 10, includeRelated = true } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        try {\n            const searchTerm = query.toLowerCase().trim();\n            // Construir query base\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true);\n            // Filtrar por categoría si se especifica\n            if (category) {\n                supabaseQuery = supabaseQuery.eq(\"category_id\", category);\n            }\n            // Usar búsqueda de texto completo\n            const { data, error } = await supabaseQuery.or(\"question.ilike.%\".concat(searchTerm, \"%,answer.ilike.%\").concat(searchTerm, \"%\")).order(\"popularity\", {\n                ascending: false\n            }).limit(limit * 2) // Obtener más para filtrar después\n            ;\n            if (error) {\n                console.error(\"Error searching FAQs:\", error);\n                return [];\n            }\n            // Filtrar y ordenar resultados\n            let results = (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n            // Filtrar por tags y procedimientos relacionados si includeRelated es true\n            if (includeRelated) {\n                results = results.filter((faq)=>{\n                    const questionMatch = faq.question.toLowerCase().includes(searchTerm);\n                    const answerMatch = faq.answer.toLowerCase().includes(searchTerm);\n                    const tagMatch = faq.tags.some((tag)=>tag.toLowerCase().includes(searchTerm));\n                    const procedureMatch = faq.relatedProcedures.some((proc)=>proc.toLowerCase().includes(searchTerm));\n                    return questionMatch || answerMatch || tagMatch || procedureMatch;\n                });\n            }\n            // Ordenar por relevancia\n            results.sort((a, b)=>{\n                const aScore = this.calculateRelevanceScore(a, searchTerm);\n                const bScore = this.calculateRelevanceScore(b, searchTerm);\n                return bScore - aScore;\n            });\n            const finalResults = results.slice(0, limit);\n            const responseTime = Date.now() - startTime;\n            // Registrar analytics\n            if (finalResults.length === 0) {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackNoResults(query, category);\n            } else {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackSearch(query, finalResults.length, responseTime, category);\n            }\n            return finalResults;\n        } catch (error) {\n            console.error(\"Error in searchFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Calcular puntuación de relevancia\n   */ calculateRelevanceScore(faq, searchTerm) {\n        let score = 0;\n        const term = searchTerm.toLowerCase();\n        // Coincidencia exacta en pregunta (peso alto)\n        if (faq.question.toLowerCase().includes(term)) {\n            score += 100;\n        }\n        // Coincidencia en respuesta (peso medio)\n        if (faq.answer.toLowerCase().includes(term)) {\n            score += 50;\n        }\n        // Coincidencia en tags (peso medio)\n        faq.tags.forEach((tag)=>{\n            if (tag.toLowerCase().includes(term)) {\n                score += 30;\n            }\n        });\n        // Popularidad (peso bajo)\n        score += faq.popularity * 0.1;\n        return score;\n    }\n    /**\n   * Obtener FAQs más populares desde Supabase\n   */ async getPopularFAQs() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        try {\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error fetching popular FAQs:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getPopularFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQ por ID desde Supabase\n   */ async getFAQById(id) {\n        try {\n            var _data_faq_categories;\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"id\", id).eq(\"is_active\", true).single();\n            if (error) {\n                console.error(\"Error fetching FAQ by ID:\", error);\n                return null;\n            }\n            const faq = this.mapFAQFromDB(data, (_data_faq_categories = data.faq_categories) === null || _data_faq_categories === void 0 ? void 0 : _data_faq_categories.name);\n            // Registrar visualización y actualizar contador\n            _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackFAQView(faq.id, faq.question);\n            // Incrementar view_count en la base de datos\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").update({\n                view_count: (data.view_count || 0) + 1\n            }).eq(\"id\", id);\n            return faq;\n        } catch (error) {\n            console.error(\"Error in getFAQById:\", error);\n            return null;\n        }\n    }\n    /**\n   * Obtener estadísticas del FAQ desde Supabase\n   */ async getFAQStats() {\n        try {\n            // Obtener estadísticas de FAQs\n            const { data: faqStats, error: faqError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"popularity, category_id\").eq(\"is_active\", true);\n            if (faqError) {\n                console.error(\"Error fetching FAQ stats:\", faqError);\n                return {\n                    totalFAQs: 0,\n                    totalCategories: 0,\n                    averagePopularity: 0,\n                    mostPopularCategory: \"\"\n                };\n            }\n            // Obtener estadísticas de categorías directamente\n            const { data: categoryStats, error: categoryError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"id, name\").eq(\"is_active\", true);\n            if (categoryError) {\n                console.error(\"Error fetching category stats:\", categoryError);\n            }\n            // Calcular estadísticas\n            const totalFAQs = (faqStats === null || faqStats === void 0 ? void 0 : faqStats.length) || 0;\n            const totalCategories = (categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.length) || 0;\n            const averagePopularity = totalFAQs > 0 ? Math.round(faqStats.reduce((sum, faq)=>sum + (faq.popularity || 0), 0) / totalFAQs) : 0;\n            // Calcular categoría más popular sin llamar a getCategories()\n            const categoryCount = new Map();\n            faqStats === null || faqStats === void 0 ? void 0 : faqStats.forEach((faq)=>{\n                if (faq.category_id) {\n                    categoryCount.set(faq.category_id, (categoryCount.get(faq.category_id) || 0) + 1);\n                }\n            });\n            let mostPopularCategory = \"\";\n            let maxCount = 0;\n            categoryCount.forEach((count, categoryId)=>{\n                if (count > maxCount) {\n                    maxCount = count;\n                    const category = categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.find((cat)=>cat.id === categoryId);\n                    mostPopularCategory = (category === null || category === void 0 ? void 0 : category.name) || \"\";\n                }\n            });\n            return {\n                totalFAQs,\n                totalCategories,\n                averagePopularity,\n                mostPopularCategory\n            };\n        } catch (error) {\n            console.error(\"Error in getFAQStats:\", error);\n            return {\n                totalFAQs: 0,\n                totalCategories: 0,\n                averagePopularity: 0,\n                mostPopularCategory: \"\"\n            };\n        }\n    }\n    constructor(){\n        this.themesCache = new Map();\n        this.cacheExpiry = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.lastCacheUpdate = 0;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/faqService.ts\n"));

/***/ })

});