-- Corrected FAQ Chunk 19
-- Questions 271 to 285

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Plataforma De Juventudes?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'plataforma'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Plataforma De Juventudes se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'inscripción', 'plataforma', 'juventudes', 'puede'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Plataforma De Juventudes se puede hace presencial?', 'Si, En la Dirección de Ciudadanía Juvenil', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'inscripción', 'plataforma', 'juventudes', 'puede'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Concejo Municipal De Juventudes?', 'Los Consejos de Juventudes son mecanismos autónomos de participación, concertación, vigilancia y control de la gestión pública e interlocución de los y las jóvenes en relación con las agendas territoriales de las LEY ESTATUTARIA 1622 DE 2013 MODIFICADA POR LA LEY ESTATUTARIA 1885 DE 2018 43 juventudes, ante institucionalidad pública de cada ente territorial al que pertenezcan, y desde las cuales deberán canalizarse los acuerdos de los y las jóvenes sobre las alternativas de solución a las necesidades y problemáticas de sus contextos y la visibilización de sus potencialidades y propuestas para su desarrollo social, político y cultural ante los gobiernos territoriales y nacional. Nota: está en curso modificación de la normatividad por parte del congreso. Requisitos para ingresar al programa al Programa Concejo Municipal De Juventudes Estar en el rango de edad establecido en la presente ley. Los jóvenes entre 14 y 17 años deberán presentar copia del registro civil de nacimiento o tarjeta de identidad. Así mismo los jóvenes entre 18 y 28 años deberán presentar la cédula de ciudadanía o contraseña. Tener domicilio o demostrar que realiza una actividad laboral, educativa o de trabajo comunitario, en el territorio al cual aspira representar, mediante declaración juramentada ante una Notaría. Estar inscrito en una lista presentada por los jóvenes independientes, o por un movimiento o partido político con personería jurídica. En el caso de los procesos y prácticas organizativas juveniles ser postulado por una de ellas. Presentar ante la respectiva Registraduría, una propuesta de trabajo que indique los lineamientos a seguir como consejero de juventud, durante su periodo.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['requisito', 'registro', 'linea', 'programa', 'consiste', 'concejo', 'municipal', 'juventudes'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa al Programa Concejo Municipal De Juventudes?', 'En los sitios o links asignados por la registraduría Municipal.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la postulación al programa al Programa Concejo Municipal De Juventudes?', 'La postulación No, pero debe anexar la declaración juramentada que reside o trabaja en el Municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'tiene', 'postulación'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa al Programa Concejo Municipal De Juventudes se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'inscripción', 'concejo', 'municipal'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Concejo Municipal De Juventudes Se puede hacer presencial?', 'Si, en la registraría Municipal de Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'inscripción', 'concejo', 'municipal', 'juventudes'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'Consiste en un programa de la red de bibliotecas, que representa una experiencia que permite desarrollar el pensamiento, el lenguaje, la imaginación, la creatividad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'La inscripción se realiza de forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'promoción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'promoción'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Promoción De Lectura, Escritura Y Oralidad – LEO se puede hacer en línea?', 'forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'inscripción', 'promoción', 'lectura', 'escritura'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Promoción De Lectura, Escritura Y Oralidad – LEO Se puede hace presencial?', 'Si, en las instalaciones de la Biblioteca Municipal o en todas las sedes.', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'inscripción', 'promoción', 'lectura', 'escritura'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Forma TIC de la Red de Bibliotecas?', 'Consiste en un servicio de carácter pedagógico que estimula el uso adecuado de las Bibliotecas y fomenta las competencias básicas en el acceso y uso de la información y la tecnología. Para lo cual se ofertan los siguientes cursos: 1. Reconoce tu biblioteca 2. Aprende a explorar fuentes de información 3.Tutorias en información básica 4. Información y TIC en finanzas personales 5. Técnicas para el aprendizaje efectivo 6. Herramientas para el aprendizaje autónomo del inglés 7. Iniciación a la creación literaria 8. Seguridad en información y Tic 9. Prácticas de lectura, Escritura y Oralidad-LEO 10.Sensibilizacion Ambiental Requisitos para ingresar al Programa Forma TIC de la Red de Bibliotecas Revisar oferta de cursos Consultar en las bibliotecas o realizar requerimiento a la biblioteca', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['requisito', 'programa', 'servicio', 'consiste', 'forma', 'bibliotecas', 'consiste'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al Programa Forma TIC de la Red de Bibliotecas?', 'Se realiza de forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia y en todas las sedes. Correo: <EMAIL> Teléfono: 8844444 Ext 3605', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'forma'], 2);
