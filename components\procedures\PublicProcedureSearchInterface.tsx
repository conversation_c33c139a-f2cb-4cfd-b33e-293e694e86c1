'use client'

import React, { useState, useMemo } from 'react'
import { Search, Filter, Grid, List, Eye, Clock, DollarSign, Building2, FileText, BarChart3 } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { PublicProcedureDetailModal } from './PublicProcedureDetailModal'

interface Dependency {
  id: string
  name: string
  acronym?: string
  description?: string
  contact_email?: string
  contact_phone?: string
  address?: string
}

interface Subdependency {
  id: string
  name: string
}

interface Procedure {
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  category?: string
  difficulty_level?: number
  popularity_score?: number
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  dependency?: Dependency
  subdependency?: Subdependency
  procedure_type?: 'TRAMITE' | 'OPA'
}

interface PublicProcedureSearchInterfaceProps {
  procedures: Procedure[]
  dependencies: Dependency[]
  subdependencies: Subdependency[]
  categories: string[]
}

export function PublicProcedureSearchInterface({
  procedures,
  dependencies,
  subdependencies,
  categories
}: PublicProcedureSearchInterfaceProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedDependency, setSelectedDependency] = useState('')
  const [selectedSubdependency, setSelectedSubdependency] = useState('')
  const [selectedProcedureType, setSelectedProcedureType] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedProcedure, setSelectedProcedure] = useState<Procedure | null>(null)

  // Get filtered subdependencies based on selected dependency
  const filteredSubdependencies = useMemo(() => {
    if (!selectedDependency) return subdependencies

    // Filter subdependencies that belong to the selected dependency
    return subdependencies.filter(sub => {
      // Find procedures that belong to this subdependency and the selected dependency
      return procedures.some(proc =>
        proc.subdependency?.id === sub.id &&
        proc.dependency?.id === selectedDependency
      )
    })
  }, [selectedDependency, subdependencies, procedures])

  // Filter and sort procedures
  const filteredProcedures = useMemo(() => {
    let filtered = procedures.filter(procedure => {
      const matchesSearch = !searchTerm ||
        procedure.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        procedure.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        procedure.dependency?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        procedure.subdependency?.name.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesCategory = !selectedCategory || procedure.category === selectedCategory
      const matchesDependency = !selectedDependency || procedure.dependency?.id === selectedDependency
      const matchesSubdependency = !selectedSubdependency || procedure.subdependency?.id === selectedSubdependency
      const matchesProcedureType = !selectedProcedureType || procedure.procedure_type === selectedProcedureType

      return matchesSearch && matchesCategory && matchesDependency && matchesSubdependency && matchesProcedureType
    })

    // Sort procedures
    filtered.sort((a, b) => {
      let aValue: any, bValue: any
      
      switch (sortBy) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'cost':
          aValue = a.cost || 0
          bValue = b.cost || 0
          break
        case 'popularity':
          aValue = a.popularity_score || 0
          bValue = b.popularity_score || 0
          break
        case 'dependency':
          aValue = a.dependency?.name || ''
          bValue = b.dependency?.name || ''
          break
        default:
          aValue = a.name
          bValue = b.name
      }

      if (typeof aValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      } else {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue
      }
    })

    return filtered
  }, [procedures, searchTerm, selectedCategory, selectedDependency, selectedSubdependency, selectedProcedureType, sortBy, sortOrder])

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCategory('')
    setSelectedDependency('')
    setSelectedSubdependency('')
    setSelectedProcedureType('')
  }

  // Clear subdependency when dependency changes
  const handleDependencyChange = (value: string) => {
    setSelectedDependency(value)
    setSelectedSubdependency('') // Clear subdependency when dependency changes
  }

  const formatCost = (cost?: number) => {
    if (!cost || cost === 0) return 'Gratuito'
    return `$${cost.toLocaleString('es-CO')}`
  }

  const formatResponseTime = (time?: string) => {
    if (!time) return 'No especificado'
    return time
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <Search className="mr-2 h-5 w-5 text-chia-blue-600" />
                Buscar Trámites y OPAs
              </CardTitle>
              <CardDescription>
                Encuentra información detallada sobre procedimientos municipales
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por nombre, descripción, dependencia o subdependencia..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Categoría
                </label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todas las categorías" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas las categorías</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Dependencia
                </label>
                <Select value={selectedDependency} onValueChange={handleDependencyChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todas las dependencias" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas las dependencias</SelectItem>
                    {dependencies.map((dependency) => (
                      <SelectItem key={dependency.id} value={dependency.id}>
                        {dependency.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Subdependencia
                </label>
                <Select
                  value={selectedSubdependency}
                  onValueChange={setSelectedSubdependency}
                  disabled={!selectedDependency}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todas las subdependencias" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas las subdependencias</SelectItem>
                    {filteredSubdependencies.map((subdependency) => (
                      <SelectItem key={subdependency.id} value={subdependency.id}>
                        {subdependency.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Tipo de Procedimiento
                </label>
                <Select value={selectedProcedureType} onValueChange={setSelectedProcedureType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todos los tipos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos los tipos</SelectItem>
                    <SelectItem value="TRAMITE">Trámites</SelectItem>
                    <SelectItem value="OPA">OPAs</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Ordenar por
                </label>
                <div className="flex space-x-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Nombre</SelectItem>
                      <SelectItem value="cost">Costo</SelectItem>
                      <SelectItem value="popularity">Popularidad</SelectItem>
                      <SelectItem value="dependency">Dependencia</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  >
                    {sortOrder === 'asc' ? '↑' : '↓'}
                  </Button>
                </div>
              </div>

              {/* Clear Filters Button */}
              <div className="flex items-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="w-full"
                >
                  Limpiar Filtros
                </Button>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="flex justify-between items-center mt-4 text-sm text-gray-600">
            <span>
              {filteredProcedures.length} de {procedures.length} procedimientos encontrados
            </span>
            {(searchTerm || selectedCategory || selectedDependency) && (
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Limpiar búsqueda
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Procedures Results */}
      {filteredProcedures.length > 0 ? (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {filteredProcedures.map((procedure) => (
            <Card key={procedure.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 line-clamp-2">
                        {procedure.name}
                      </h3>
                      {procedure.dependency && (
                        <div className="flex items-center mt-1 text-sm text-gray-600">
                          <Building2 className="h-4 w-4 mr-1" />
                          {procedure.dependency.name}
                          {procedure.subdependency && (
                            <span className="ml-2 text-gray-500">
                              • {procedure.subdependency.name}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Description */}
                  {procedure.description && (
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {procedure.description}
                    </p>
                  )}

                  {/* Metadata */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-gray-600">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{formatCost(procedure.cost)}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{formatResponseTime(procedure.response_time)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Category, Type and Online Status */}
                  <div className="flex justify-between items-center flex-wrap gap-2">
                    <div className="flex gap-2">
                      {procedure.procedure_type && (
                        <Badge
                          variant={procedure.procedure_type === 'TRAMITE' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {procedure.procedure_type}
                        </Badge>
                      )}
                      {procedure.category && (
                        <Badge variant="outline" className="text-xs">
                          {procedure.category}
                        </Badge>
                      )}
                    </div>
                    {procedure.online_available && (
                      <Badge className="text-xs bg-chia-green-100 text-chia-green-800">
                        Disponible en línea
                      </Badge>
                    )}
                  </div>

                  {/* Action Button - Only View Details */}
                  <div className="pt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full"
                      onClick={() => setSelectedProcedure(procedure)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Ver Información Completa
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No se encontraron procedimientos
            </h3>
            <p className="text-gray-500 mb-4">
              Intenta ajustar tus criterios de búsqueda o filtros
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Limpiar filtros
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Procedure Detail Modal */}
      {selectedProcedure && (
        <PublicProcedureDetailModal
          procedure={selectedProcedure}
          onClose={() => setSelectedProcedure(null)}
        />
      )}
    </div>
  )
}
