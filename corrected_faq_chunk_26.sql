-- Corrected FAQ Chunk 26
-- Questions 376 to 383

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costos comunalitos en acción?', 'No tiene ningún costo', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene', 'costos', 'comunalitos', 'acción', 'tiene'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción a comunalitos en acción se puede hacer en línea?', 'No, teniendo en cuenta que como es un grupo de niños y niñas, los consentimientos informados y documentación de los menores se requieren en físico.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['inscripción', 'comunalitos', 'acción', 'puede', 'hacer'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial comunalitos en acción?', 'Si, teniendo en cuenta que como es un grupo de niños y niñas, los consentimientos informados y documentación de los menores se requieren en físico. Cra 7 No. 12-100 Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['presencial', 'puede', 'hace', 'comunalitos', 'acción'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste la mesa de niños, niñas y adolescentes?', 'La mesa Municipal de niños, niñas y adolescentes es una instancia de participación dentro del Consejo Municipal de Política Social de Chía, que tiene el objetivo de representar a todos los niños, niñas y adolescentes del municipio de Chía ante la administración municipal, constituyéndose como un espacio para forjar lazos entre los participantes, y la construcción de nuevos liderazgos. Requisitos para ingresar a la mesa de niños, niñas y adolescentes Ser Residente del Municipio de Chía​ Tener entre 7 y 17 años de edad. Vincularse a través de la Secretaria de Participación Ciudadana.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['requisito', 'consiste', 'mesa', 'niños', 'niñas', 'adolescentes'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción a la mesa de niños, niñas y adolescentes??', 'Con la presentación del documento de identidad del menor y la voluntad de asistir y ser parte de la mesa y de las actividades que se desarrollan.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['documento', 'cómo', 'realiza', 'inscripción', 'mesa', 'niños'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse y participar de la mesa de niños, niñas y adolescentes??', 'Es totalmente gratuito.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['costo', 'gratuito', 'tiene', 'inscribirse', 'participar', 'mesa'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción la mesa de niños, niñas y adolescentes se puede hacer en línea?', 'Si, enviando un correo electrónico a la Secretaria de Participación Ciudadana con los datos personales y de contacto para informar los horarios, fechas y lugares de las diferentes actividades.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['horario', 'inscripción', 'mesa', 'niños', 'niñas', 'adolescentes'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer presencial la mesa de niños, niñas y adolescentes?', 'Si, en cualquier momento se puede presentar a la secretaria y aportar algunos datos para informar las reuniones y diferentes actividades. Dirección: Cra 7 No. 12-100 Chía. Correo electrónico participació*************', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['presencial', 'puede', 'hacer', 'mesa', 'niños'], 5);
