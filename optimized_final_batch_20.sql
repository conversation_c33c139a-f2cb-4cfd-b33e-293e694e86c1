-- Optimized Final Completion Batch 20
-- Questions 364 to 378
-- Total questions in batch: 15

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al Consejo de Turismo se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'REGISTRO NACIONAL DE TURISMO'), 
 ARRAY['inscripción', 'consejo', 'turismo', 'puede', 'hacer'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al Consejo de Turismo se puede hace presencial?', 'Si, en la cr. 10 No. 8-72, Centro Comercial el Curubito, Chía, Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'REGISTRO NACIONAL DE TURISMO'), 
 ARRAY['presencial', 'inscripción', 'consejo', 'turismo', 'puede', 'hace'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste SIT CHÍA?', 'El Sistema de Información Turística de Chía – SITCHIA- es una iniciativa de la Dirección de Turismo del Municipio, que se está trabajando desde el año 2020, con el objetivo de contar con datos estadísticos del sector, a través de información primaria y secundaria recibida de diferentes fuentes. Recolectar, procesar y analizar información de la actividad turística con el fin de poder ser utilizada en la elaboración de informes y documentos útiles para una coherente toma de decisiones;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al SIT CHÍA?', 'https://turismo.chia-cundinamarca.gov.co/sitchia/', 
 (SELECT id FROM faq_themes WHERE name = 'REGISTRO NACIONAL DE TURISMO'), 
 ARRAY['cómo', 'realiza', 'inscripción', 'chía', 'https'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción SIT CHÍA?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'REGISTRO NACIONAL DE TURISMO'), 
 ARRAY['costo', 'tiene', 'inscripción', 'chía'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste presupuesto participativo?', 'El Congreso de la República a través de la Ley Estatutaria 1757 de 2015 “Por la cual se dictan disposiciones en materia de promoción y protección del derecho a la participación democrática”, en sus artículos 90, 91, 92, 93 y 100, define el proceso del Presupuesto Participativo, así: (…) ARTÍCULO 90. “Definición. El proceso del presupuesto participativo es un mecanismo de asignación equitativa, racional, eficiente, eficaz y transparente de los recursos públicos, que fortalece las relaciones Estado-Sociedad Civil. Para ello, los gobiernos regionales y gobiernos locales promueven el desarrollo de mecanismos y estrategias de participación en la programación de sus presupuestos, así como en la vigilancia y fiscalización de la gestión de los recursos públicos (…). Requisitos para ingresar al programa presupuesto participativo Ser Residente del Municipio de Chía​ Asistir a las convocatorias realizadas por la Alcaldía Construcción de las propuestas por parte de los ciudadanos, grupos poblacionales, instancias de participación o grupos de interés. Evaluación de factibilidad Priorización por medio de votación por parte de los ciudadanos. Selección de las ideas Ejecución por parte de las dependencias responsables Seguimiento a la Ejecución', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'programa', 'consiste', 'presupuesto', 'participativo', 'congreso', 'república'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción a presupuesto participativo?', 'Se realiza a través de las mesas de trabajo convocadas y realizadas por la Alcaldía.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'realiza', 'inscripción', 'presupuesto', 'participativo'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo ingresar al programa de presupuesto participativo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'programa', 'tiene', 'ingresar', 'presupuesto'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción a presupuesto participativo se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['inscripción', 'presupuesto', 'participativo', 'puede', 'hacer'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial la inscripción a presupuesto participativo?', 'Si, en los lugares donde se realicen las mesas. PROGRAMA: COMUNALITOS EN ACCIÓN', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['presencial', 'programa', 'puede', 'hace', 'inscripción', 'presupuesto'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste comunalitos en acción?', 'Es una estrategia dirigida a niños y niñas de promoción y fortalecimiento a la participación ciudadana, uno de sus objetivos es generar un sentido de pertenencia, liderazgo, compromiso y colaboración con la comunidad, con el desarrollo de procesos pedagógicos, formativos y democráticos aplicados a la gestión comunitaria. La Secretaría de Participación Ciudadana y Acción Comunitaria, promueve la estrategia comunalitos con el propósito que todas las Organizaciones comunales cuenten con este grupo de niños y niñas. Requisitos para ingresar al programa comunalitos en acción Tener entre 7 y 13 años de edad. Vincularse a través de las juntas de acción comunal y teniendo en cuenta el sitio de residencia Contar con documento de identidad Consentimiento informado del acudiente del menor', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'documento', 'programa', 'consiste', 'comunalitos', 'acción', 'estrategia', 'dirigida'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción a comunalitos en acción?', 'La inscripción se realiza con el padrino de la Junta de Acción Comunal', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'realiza', 'inscripción', 'comunalitos', 'acción'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costos comunalitos en acción?', 'No tiene ningún costo', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene', 'costos', 'comunalitos', 'acción', 'tiene'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción a comunalitos en acción se puede hacer en línea?', 'No, teniendo en cuenta que como es un grupo de niños y niñas, los consentimientos informados y documentación de los menores se requieren en físico.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['inscripción', 'comunalitos', 'acción', 'puede', 'hacer'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial comunalitos en acción?', 'Si, teniendo en cuenta que como es un grupo de niños y niñas, los consentimientos informados y documentación de los menores se requieren en físico. Cra 7 No. 12-100 Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['presencial', 'puede', 'hace', 'comunalitos', 'acción'], 10);
