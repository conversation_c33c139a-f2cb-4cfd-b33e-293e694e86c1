-- =====================================================
-- ESQUEMA DE BASE DE DATOS PARA SISTEMA FAQ MUNICIPAL
-- Sistema de Preguntas Frecuentes de Chía
-- =====================================================

-- Tabla para temas de FAQ (categorización por dependencia/subdependencia)
CREATE TABLE IF NOT EXISTS faq_themes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    dependency_id UUID REFERENCES dependencies(id) ON DELETE CASCADE,
    subdependency_id UUID REFERENCES subdependencies(id) ON DELETE CASCADE,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Índices para optimización
    CONSTRAINT faq_themes_dependency_check 
        CHECK (dependency_id IS NOT NULL),
    CONSTRAINT faq_themes_unique_name_per_dependency 
        UNIQUE (name, dependency_id, subdependency_id)
);

-- Tabla principal de preguntas frecuentes municipales
CREATE TABLE IF NOT EXISTS municipal_faqs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    theme_id UUID NOT NULL REFERENCES faq_themes(id) ON DELETE CASCADE,
    keywords TEXT[] DEFAULT '{}',
    
    -- Campos para búsqueda y análisis
    search_vector TSVECTOR,
    view_count INTEGER DEFAULT 0,
    helpful_votes INTEGER DEFAULT 0,
    unhelpful_votes INTEGER DEFAULT 0,
    popularity_score INTEGER DEFAULT 0,
    
    -- Metadatos
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Índices para optimización de búsqueda
    CONSTRAINT municipal_faqs_question_not_empty 
        CHECK (length(trim(question)) > 0),
    CONSTRAINT municipal_faqs_answer_not_empty 
        CHECK (length(trim(answer)) > 0)
);

-- Tabla para analíticas de FAQ municipal
CREATE TABLE IF NOT EXISTS municipal_faq_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255),
    event_type VARCHAR(50) NOT NULL, -- 'view', 'search', 'vote', 'helpful', 'unhelpful'
    faq_id UUID REFERENCES municipal_faqs(id) ON DELETE CASCADE,
    theme_id UUID REFERENCES faq_themes(id) ON DELETE CASCADE,
    dependency_id UUID REFERENCES dependencies(id) ON DELETE CASCADE,
    subdependency_id UUID REFERENCES subdependencies(id) ON DELETE CASCADE,
    
    -- Datos de búsqueda
    search_query TEXT,
    results_count INTEGER,
    response_time INTEGER, -- en milisegundos
    
    -- Contexto de uso
    context VARCHAR(100), -- 'web', 'mobile', 'api'
    user_agent TEXT,
    ip_address INET,
    
    -- Metadatos adicionales
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT now(),
    
    -- Índices para análisis
    CONSTRAINT municipal_faq_analytics_event_type_check 
        CHECK (event_type IN ('view', 'search', 'vote', 'helpful', 'unhelpful', 'filter'))
);

-- Tabla para historial de búsquedas FAQ
CREATE TABLE IF NOT EXISTS municipal_faq_search_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255),
    search_query TEXT NOT NULL,
    dependency_filter VARCHAR(10), -- código de dependencia
    subdependency_filter VARCHAR(10), -- código de subdependencia
    theme_filter UUID,
    results_count INTEGER DEFAULT 0,
    response_time INTEGER DEFAULT 0,
    context VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT now(),
    
    -- Índice para análisis de patrones de búsqueda
    CONSTRAINT municipal_faq_search_query_not_empty 
        CHECK (length(trim(search_query)) > 0)
);

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN DE RENDIMIENTO
-- =====================================================

-- Índices para faq_themes
CREATE INDEX IF NOT EXISTS idx_faq_themes_dependency_id ON faq_themes(dependency_id);
CREATE INDEX IF NOT EXISTS idx_faq_themes_subdependency_id ON faq_themes(subdependency_id);
CREATE INDEX IF NOT EXISTS idx_faq_themes_active ON faq_themes(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_faq_themes_display_order ON faq_themes(display_order);

-- Índices para municipal_faqs
CREATE INDEX IF NOT EXISTS idx_municipal_faqs_theme_id ON municipal_faqs(theme_id);
CREATE INDEX IF NOT EXISTS idx_municipal_faqs_active ON municipal_faqs(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_municipal_faqs_popularity ON municipal_faqs(popularity_score DESC);
CREATE INDEX IF NOT EXISTS idx_municipal_faqs_keywords ON municipal_faqs USING GIN(keywords);

-- Índice de búsqueda de texto completo
CREATE INDEX IF NOT EXISTS idx_municipal_faqs_search_vector ON municipal_faqs USING GIN(search_vector);

-- Índices para analíticas
CREATE INDEX IF NOT EXISTS idx_municipal_faq_analytics_created_at ON municipal_faq_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_municipal_faq_analytics_event_type ON municipal_faq_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_municipal_faq_analytics_dependency_id ON municipal_faq_analytics(dependency_id);
CREATE INDEX IF NOT EXISTS idx_municipal_faq_analytics_session_id ON municipal_faq_analytics(session_id);

-- Índices para historial de búsquedas
CREATE INDEX IF NOT EXISTS idx_municipal_faq_search_history_created_at ON municipal_faq_search_history(created_at);
CREATE INDEX IF NOT EXISTS idx_municipal_faq_search_history_query ON municipal_faq_search_history USING GIN(to_tsvector('spanish', search_query));

-- =====================================================
-- TRIGGERS PARA AUTOMATIZACIÓN
-- =====================================================

-- Función para actualizar search_vector automáticamente
CREATE OR REPLACE FUNCTION update_municipal_faq_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('spanish', COALESCE(NEW.question, '')), 'A') ||
        setweight(to_tsvector('spanish', COALESCE(NEW.answer, '')), 'B') ||
        setweight(to_tsvector('spanish', array_to_string(COALESCE(NEW.keywords, '{}'), ' ')), 'C');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para actualizar search_vector
DROP TRIGGER IF EXISTS trigger_update_municipal_faq_search_vector ON municipal_faqs;
CREATE TRIGGER trigger_update_municipal_faq_search_vector
    BEFORE INSERT OR UPDATE ON municipal_faqs
    FOR EACH ROW
    EXECUTE FUNCTION update_municipal_faq_search_vector();

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para updated_at
DROP TRIGGER IF EXISTS trigger_update_faq_themes_updated_at ON faq_themes;
CREATE TRIGGER trigger_update_faq_themes_updated_at
    BEFORE UPDATE ON faq_themes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_municipal_faqs_updated_at ON municipal_faqs;
CREATE TRIGGER trigger_update_municipal_faqs_updated_at
    BEFORE UPDATE ON municipal_faqs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS en las tablas
ALTER TABLE faq_themes ENABLE ROW LEVEL SECURITY;
ALTER TABLE municipal_faqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE municipal_faq_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE municipal_faq_search_history ENABLE ROW LEVEL SECURITY;

-- Política para acceso público de lectura a temas FAQ
CREATE POLICY "Public read access to active FAQ themes" ON faq_themes
    FOR SELECT USING (is_active = true);

-- Política para acceso público de lectura a FAQs
CREATE POLICY "Public read access to active municipal FAQs" ON municipal_faqs
    FOR SELECT USING (is_active = true);

-- Política para inserción de analíticas (acceso público para tracking)
CREATE POLICY "Public insert access to FAQ analytics" ON municipal_faq_analytics
    FOR INSERT WITH CHECK (true);

-- Política para inserción de historial de búsquedas
CREATE POLICY "Public insert access to FAQ search history" ON municipal_faq_search_history
    FOR INSERT WITH CHECK (true);

-- Políticas para funcionarios autenticados (acceso completo)
CREATE POLICY "Authenticated users full access to FAQ themes" ON faq_themes
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users full access to municipal FAQs" ON municipal_faqs
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users read access to FAQ analytics" ON municipal_faq_analytics
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users read access to FAQ search history" ON municipal_faq_search_history
    FOR SELECT USING (auth.role() = 'authenticated');

-- =====================================================
-- COMENTARIOS PARA DOCUMENTACIÓN
-- =====================================================

COMMENT ON TABLE faq_themes IS 'Temas de preguntas frecuentes organizados por dependencia y subdependencia municipal';
COMMENT ON TABLE municipal_faqs IS 'Preguntas frecuentes del sistema municipal de Chía con búsqueda optimizada';
COMMENT ON TABLE municipal_faq_analytics IS 'Analíticas de uso del sistema FAQ para mejora continua';
COMMENT ON TABLE municipal_faq_search_history IS 'Historial de búsquedas para análisis de patrones ciudadanos';

COMMENT ON COLUMN faq_themes.dependency_id IS 'Referencia a la dependencia municipal (obligatorio)';
COMMENT ON COLUMN faq_themes.subdependency_id IS 'Referencia a la subdependencia municipal (opcional)';
COMMENT ON COLUMN municipal_faqs.search_vector IS 'Vector de búsqueda de texto completo generado automáticamente';
COMMENT ON COLUMN municipal_faqs.keywords IS 'Palabras clave para búsqueda optimizada';
COMMENT ON COLUMN municipal_faq_analytics.event_type IS 'Tipo de evento: view, search, vote, helpful, unhelpful, filter';
COMMENT ON COLUMN municipal_faq_search_history.dependency_filter IS 'Código de dependencia usado como filtro';
COMMENT ON COLUMN municipal_faq_search_history.subdependency_filter IS 'Código de subdependencia usado como filtro';
