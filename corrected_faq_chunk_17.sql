-- Corrected FAQ Chunk 17
-- Questions 241 to 255

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo ingresar al programa habitante de Calle?', 'No,', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'ingresar', 'habitante'], 29);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa habitante de Calle se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'habitante', 'calle', 'puede'], 30);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer presencial la inscripción al programa habitante de Calle?', 'Si en el centro transitorio de protección de Chía, pues es necesaria la activación de la ruta de atención para las atenciones al habitante de calle. TEMA : PROGRAMARENTA JOVEN', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción'], 31);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Renta Joven?', 'El programa Renta Joven es la evolución de Jóvenes en Acción y surge con el ánimo de contribuir a la inclusión social y económica de las juventudes. Este programa es de Orden Nacional que fomenta el acceso, permanencia y graduación de la educación superior y formación complementaria para acompañar el proceso durante un ciclo educativo: técnico, tecnólogo o pregrado, mediante la entrega de transferencias monetarias condicionadas. Renta Joven comienza a operar en 2024. Requisitos para ingresar al programa Renta Joven Realizar pre-registro en las fechas establecidas en la plataforma de prosperidad social en la plataforma de renta joven. Esperar comunicado del DPS en el correo electrónico registrado en el pre-registro.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'registro', 'programa', 'consiste', 'renta', 'joven'], 32);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Renta Joven?', 'Es necesario realizar el Pre-registro: Jóvenes en acción (prosperidadsocial.gov.co)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['registro', 'programa', 'cómo', 'realiza', 'inscripción', 'renta'], 33);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa Renta Joven?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'renta'], 34);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Renta Joven se puede hacer en línea?', 'Si, Jovenes en acción (prosperidadsocial.gov.co)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'renta', 'joven', 'puede'], 35);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer presencial la inscripción al programa Renta Joven?', 'No TEMA : PROGRAMA ATENCIÓN INTEGRAL PARA ADULTO MAYOR', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción'], 36);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Atención Integral Para el Adulto Mayor?', 'Este programa busca ocupar el tiempo libre de los adultos mayores, en el que se capacitan con diferentes talleres y actividades diarias, adicional realizan actividades físicas y juegos autóctonos. El programa cuenta con 13 puntos de atención en diferentes sectores y veredas del Municipio y se atienden dos grupos más en casa día Fagua, durante las actividades diarias se brinda a todos los adultos mayores un refrigerio y almuerzo, otra de las modalidades es la atención domiciliaria en la que se realiza entrega de un paquete alimentario mensual y la última modalidad es la atención prioritaria que es atendida en casa día Fagua en la que se brindan talleres, actividades y desayuno, refrigerio y almuerzo. Requisitos para ingresar al programa Atención Integral Para el Adulto Mayor Sisbén del Municipio de Chía Fotocopia de la cedula de ciudadanía Fotocopia de un servicio público (Luz o Agua)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'programa', 'servicio', 'consiste', 'atención', 'integral', 'para'], 37);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Atención Integral Para el Adulto Mayor?', 'Se debe acercar a la Dirección de Acción Social en la Cra. 7 # 12-100, para inscribirse en la base de datos de espera.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'atención'], 38);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa Atención Integral Para el Adulto Mayor?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'atención'], 39);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Atención Integral Para el Adulto Mayor se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'atención', 'integral', 'para'], 40);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Atención Integral Para el Adulto Mayor Se puede hacer presencial?', 'Si, en la CRA 7 NO. 12-100. TEMA : PROGRAMA PRIMERA INFANCIA-JARDINES SOCIALES', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'inscripción', 'atención', 'integral', 'para'], 41);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Primera Infancia-Jardines Sociales?', 'El programa social de la primera infancia está orientado en la atención de niñas y niños del Municipio Chía que se encuentran entre las edades de 0 a 5 años. Este busca la atención integral de los niños, niñas beneficiadas de nuestros jardines sociales, logrando así atender a la población más vulnerable del Municipio. El programa cuenta con una cobertura de 580 cupos para que nuestros niños, niñas desarrollen habilidades sociales, emocionales y cognitivas a través de la atención integral, la cual está dirigida por profesionales en el área de educación primaria y equipo psicosocial. Requisitos para ingresar al programa Primera Infancia-Jardines Sociales Fotocopia del registro civil Fotocopia del carnet de vacunas Certificación de afiliación a EPS Certificado médico de: Audiometría, Optometría y Odontología Certificado de crecimiento y desarrollo Certificado del Sisbén 5 fotos 3*4 Fotocopia del último recibo de energía o agua de donde reside Fotocopia de documento del acudiente', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'documento', 'certificado', 'registro', 'programa', 'consiste', 'primera', 'infancia'], 42);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Primera Infancia-Jardines Sociales?', 'La inscripción se realiza con el registro civil del menor para verificar la cobertura que se cuenta en los jardines ubicados en las tres veredas del Municipio. Sus datos quedan registrados en la base de datos del programa de la Dirección de Acción Social en la Cra. 7 # 12-100, allí se envía los datos a las coordinadoras de los Jardines Sociales y se asigna el cupo dependiendo del Sector o Vereda de donde viva la familia.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['registro', 'programa', 'cómo', 'realiza', 'inscripción', 'primera'], 43);
