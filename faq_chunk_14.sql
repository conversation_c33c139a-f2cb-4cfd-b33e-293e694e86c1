-- FAQ Chunk 14
-- Questions 261 to 280

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa de Discapacidad?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'discapacidad'], 49);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa de Discapacidad se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['programa', 'inscripción', 'discapacidad', 'puede', 'hacer'], 50);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa de Discapacidad Se puede hace presencial?', 'Si, en la CRA 7 NO. 12-100.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA COLOMBIA MAYOR'), 
 ARRAY['presencial', 'programa', 'inscripción', 'discapacidad', 'puede', 'hace'], 51);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Gobierno Municipal Estudiantil?', 'Un mecanismo de participación democrática donde se les da la posibilidad a los estudiantes de las diferentes instituciones públicas y privadas de chía, de los niveles de Educación secundaria, media y técnica académica y/o técnica de autogobernar, auto gestionar su propia necesidad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Gobierno Municipal Estudiantil?', 'Link: http://inscripciongobiernoestudiantil.vuvchia.com/', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['inscripcion', 'programa', 'cómo', 'realiza', 'inscripción', 'gobierno'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa Gobierno Municipal Estudiantil?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'gobierno'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Gobierno Municipal Estudiantil se puede hacer en línea?', 'Si, link: http://inscripciongobiernoestudiantil.vuvchia.com/', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['inscripcion', 'programa', 'inscripción', 'gobierno', 'municipal', 'estudiantil'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Gobierno Municipal Estudiantil se puede hace presencial?', 'La inscripción No, pero la votación sí, en los lugares asignados.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['presencial', 'programa', 'inscripción', 'gobierno', 'municipal', 'estudiantil'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Plataforma De Juventudes?', 'Son escenarios de encuentro, articulación, coordinación e interlocución de las juventudes, de carácter autónomo. Por cada ente territorial deberá existir una plataforma. La Plataforma Local, Municipal y Distrital de Juventudes será conformada por un número plural de procesos y prácticas organizativas, así como por espacios de participación de los LEY ESTATUTARIA 1622 DE 2013 MODIFICADA POR LA LEY ESTATUTARIA 1885 DE 2018 71 y las jóvenes. Requisitos para ingresar al programa Plataforma De Juventudes Esperar la apertura de la convocatoria para la actualización de la plataforma de juventudes por parte del municipio. Copia de documento de identidad los integrantes del colectivo, proceso o practica organizativa. Diligenciamiento de formulario Carta de intención por parte del colectivo Esperar expedición del acto administrativo por parte de la personería Tener edades entre 14 a 28 años', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['requisito', 'documento', 'programa', 'consiste', 'plataforma', 'juventudes', 'escenarios'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Plataforma De Juventudes?', 'En la Dirección de Ciudadanía Juvenil, de manera presencial', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'plataforma'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Plataforma De Juventudes?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'plataforma'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Plataforma De Juventudes se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['programa', 'inscripción', 'plataforma', 'juventudes', 'puede'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Plataforma De Juventudes se puede hace presencial?', 'Si, En la Dirección de Ciudadanía Juvenil', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PLATAFORMA DE JUVENTUDES'), 
 ARRAY['presencial', 'programa', 'inscripción', 'plataforma', 'juventudes', 'puede'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Concejo Municipal De Juventudes?', 'Los Consejos de Juventudes son mecanismos autónomos de participación, concertación, vigilancia y control de la gestión pública e interlocución de los y las jóvenes en relación con las agendas territoriales de las LEY ESTATUTARIA 1622 DE 2013 MODIFICADA POR LA LEY ESTATUTARIA 1885 DE 2018 43 juventudes, ante institucionalidad pública de cada ente territorial al que pertenezcan, y desde las cuales deberán canalizarse los acuerdos de los y las jóvenes sobre las alternativas de solución a las necesidades y problemáticas de sus contextos y la visibilización de sus potencialidades y propuestas para su desarrollo social, político y cultural ante los gobiernos territoriales y nacional. Nota: está en curso modificación de la normatividad por parte del congreso. Requisitos para ingresar al programa al Programa Concejo Municipal De Juventudes Estar en el rango de edad establecido en la presente ley. Los jóvenes entre 14 y 17 años deberán presentar copia del registro civil de nacimiento o tarjeta de identidad. Así mismo los jóvenes entre 18 y 28 años deberán presentar la cédula de ciudadanía o contraseña. Tener domicilio o demostrar que realiza una actividad laboral, educativa o de trabajo comunitario, en el territorio al cual aspira representar, mediante declaración juramentada ante una Notaría. Estar inscrito en una lista presentada por los jóvenes independientes, o por un movimiento o partido político con personería jurídica. En el caso de los procesos y prácticas organizativas juveniles ser postulado por una de ellas. Presentar ante la respectiva Registraduría, una propuesta de trabajo que indique los lineamientos a seguir como consejero de juventud, durante su periodo.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES'), 
 ARRAY['requisito', 'registro', 'linea', 'programa', 'consiste', 'concejo', 'municipal', 'juventudes'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa al Programa Concejo Municipal De Juventudes?', 'En los sitios o links asignados por la registraduría Municipal.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la postulación al programa al Programa Concejo Municipal De Juventudes?', 'La postulación No, pero debe anexar la declaración juramentada que reside o trabaja en el Municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES'), 
 ARRAY['costo', 'programa', 'tiene', 'postulación'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa al Programa Concejo Municipal De Juventudes se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES'), 
 ARRAY['programa', 'inscripción', 'concejo', 'municipal'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Concejo Municipal De Juventudes Se puede hacer presencial?', 'Si, en la registraría Municipal de Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES'), 
 ARRAY['presencial', 'programa', 'inscripción', 'concejo', 'municipal', 'juventudes'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'Consiste en un programa de la red de bibliotecas, que representa una experiencia que permite desarrollar el pensamiento, el lenguaje, la imaginación, la creatividad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'La inscripción se realiza de forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA PROMOCIÓN DE LECTURA, ESCRITURA Y ORALIDAD – LEO'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'promoción'], 2);
