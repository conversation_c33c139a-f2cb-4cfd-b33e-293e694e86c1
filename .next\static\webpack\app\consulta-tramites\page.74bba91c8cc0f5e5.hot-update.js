"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/consulta-tramites/page",{

/***/ "(app-pages-browser)/./components/faq/FAQSection.tsx":
/*!***************************************!*\
  !*** ./components/faq/FAQSection.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQSection: function() { return /* binding */ FAQSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/faqService */ \"(app-pages-browser)/./lib/services/faqService.ts\");\n/* harmony import */ var _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ FAQSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * Mapeo de iconos para temas municipales\n */ const themeIcons = {\n    Receipt: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    FileCheck: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Award: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    Zap: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    FileText: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    CreditCard: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    HelpCircle: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\n/**\n * Componente principal de la sección FAQ\n */ function FAQSection(param) {\n    let { title = \"Preguntas Frecuentes\", description = \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre tr\\xe1mites y servicios municipales\", initialLimit = 10, showSearch = true, showCategoryFilter = true, showStats = true, className = \"\" } = param;\n    _s();\n    // Estados\n    const [faqs, setFaqs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [themes, setThemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTheme, setSelectedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedFAQs, setExpandedFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAllFAQs, setShowAllFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalFAQs: 0,\n        totalThemes: 0,\n        averagePopularity: 0,\n        mostPopularTheme: \"\"\n    });\n    // Cargar datos iniciales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    // Realizar búsqueda cuando cambie la query o tema\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (searchQuery.trim() || selectedTheme) {\n            performSearch();\n        }\n    }, [\n        searchQuery,\n        selectedTheme,\n        showAllFAQs,\n        initialLimit\n    ]);\n    /**\n   * Cargar datos iniciales\n   */ const loadInitialData = async ()=>{\n        const startTime = Date.now();\n        try {\n            setIsLoading(true);\n            const [themesData, popularFAQs, statsData] = await Promise.all([\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getThemes(),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(initialLimit),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQStats()\n            ]);\n            setThemes(themesData);\n            setFaqs(popularFAQs);\n            setStats(statsData);\n            // Registrar carga de sección\n            const loadTime = Date.now() - startTime;\n            _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackSectionLoad(\"faq-section\", loadTime);\n        } catch (error) {\n            console.error(\"Error loading FAQ data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Realizar búsqueda de FAQs\n   */ const performSearch = async ()=>{\n        try {\n            if (searchQuery.trim()) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].searchFAQs(searchQuery, {\n                    theme: selectedTheme || undefined,\n                    limit: showAllFAQs ? 50 : initialLimit\n                });\n                setFaqs(results);\n            } else if (selectedTheme) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQsByTheme(selectedTheme, showAllFAQs ? undefined : initialLimit);\n                setFaqs(results);\n            } else {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(showAllFAQs ? 50 : initialLimit);\n                setFaqs(results);\n            }\n        } catch (error) {\n            console.error(\"Error searching FAQs:\", error);\n        }\n    };\n    /**\n   * Alternar expansión de FAQ\n   */ const toggleFAQExpansion = (faqId)=>{\n        const newExpanded = new Set(expandedFAQs);\n        if (newExpanded.has(faqId)) {\n            newExpanded.delete(faqId);\n        } else {\n            newExpanded.add(faqId);\n            // Registrar visualización cuando se expande\n            const faq = faqs.find((f)=>f.id === faqId);\n            if (faq) {\n                _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackFAQView(faq.id, faq.question);\n            }\n        }\n        setExpandedFAQs(newExpanded);\n    };\n    /**\n   * Limpiar filtros\n   */ const clearFilters = ()=>{\n        setSearchQuery(\"\");\n        setSelectedTheme(\"\");\n        setShowAllFAQs(false);\n    };\n    /**\n   * Obtener icono de tema basado en el nombre del tema\n   */ const getThemeIcon = (themeName)=>{\n        // Mapear nombres de temas a iconos apropiados\n        const themeIconMap = {\n            \"CERTIFICADO\": \"FileCheck\",\n            \"IMPUESTOS\": \"Receipt\",\n            \"LICENCIAS\": \"Award\",\n            \"PERMISOS\": \"FileText\",\n            \"PAGOS\": \"CreditCard\",\n            \"SERVICIOS\": \"Zap\"\n        };\n        // Buscar coincidencia parcial en el nombre del tema\n        const iconKey = Object.keys(themeIconMap).find((key)=>themeName.toUpperCase().includes(key));\n        const IconComponent = iconKey ? themeIcons[themeIconMap[iconKey]] : themeIcons.HelpCircle;\n        return IconComponent;\n    };\n    // FAQs filtrados y limitados\n    const displayedFAQs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return showAllFAQs ? faqs : faqs.slice(0, initialLimit);\n    }, [\n        faqs,\n        showAllFAQs,\n        initialLimit\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-chia-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Cargando preguntas frecuentes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-8 w-8 text-chia-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalFAQs,\n                                    \" preguntas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalThemes,\n                                    \" temas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"M\\xe1s consultado: \",\n                                    stats.mostPopularTheme\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            (showSearch || showCategoryFilter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"Buscar en preguntas frecuentes...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 19\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, this),\n                            showCategoryFilter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filtrar por categor\\xeda:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: selectedTheme === \"\" ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSelectedTheme(\"\"),\n                                                className: \"h-8\",\n                                                children: \"Todos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this),\n                                            themes.map((theme)=>{\n                                                const IconComponent = getThemeIcon(theme.name);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedTheme === theme.id ? \"default\" : \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setSelectedTheme(theme.id);\n                                                        _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackCategoryFilter(theme.id);\n                                                    },\n                                                    className: \"h-8 space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: theme.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"ml-1 h-4 text-xs\",\n                                                            children: theme.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, theme.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 17\n                            }, this),\n                            (searchQuery || selectedTheme) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: clearFilters,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Limpiar filtros\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: displayedFAQs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No se encontraron preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: searchQuery ? 'No hay resultados para \"'.concat(searchQuery, '\"') : \"No hay preguntas disponibles en esta categor\\xeda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this),\n                            (searchQuery || selectedTheme) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: clearFilters,\n                                children: \"Ver todas las preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        displayedFAQs.map((faq)=>{\n                            const isExpanded = expandedFAQs.has(faq.id);\n                            const theme = themes.find((t)=>t.id === faq.themeId);\n                            const IconComponent = theme ? getThemeIcon(theme.name) : _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"transition-all duration-200 hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"cursor-pointer\",\n                                        onClick: ()=>toggleFAQExpansion(faq.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-4 w-4 text-chia-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: theme === null || theme === void 0 ? void 0 : theme.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        faq.popularityScore,\n                                                                        \"% popular\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-left text-lg leading-tight\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"ml-4 flex-shrink-0\",\n                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 19\n                                    }, this),\n                                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 25\n                                                }, this),\n                                                faq.keywords.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: faq.keywords.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: keyword\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 27\n                                                }, this),\n                                                faq.relatedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Tr\\xe1mites relacionados:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: faq.relatedProcedures.map((procedure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-chia-blue-600\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        procedure\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 33\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, faq.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        !showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowAllFAQs(true),\n                                className: \"px-8\",\n                                children: [\n                                    \"Ver m\\xe1s preguntas (\",\n                                    faqs.length - initialLimit,\n                                    \" restantes)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 15\n                        }, this),\n                        showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                onClick: ()=>setShowAllFAQs(false),\n                                className: \"px-8\",\n                                children: \"Ver menos preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQSection, \"fsnOQ7wz/gQXT5txZJwtK7fPzB8=\");\n_c = FAQSection;\nvar _c;\n$RefreshReg$(_c, \"FAQSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/faq/FAQSection.tsx\n"));

/***/ })

});