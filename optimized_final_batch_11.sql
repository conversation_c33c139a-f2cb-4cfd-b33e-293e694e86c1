-- Optimized Final Completion Batch 11
-- Questions 229 to 243
-- Total questions in batch: 15

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Colombia Mayor?', 'El Programa de Protección Social al Adulto Mayor – “Colombia Mayor” es un programa del orden Nacional que busca aumentar la protección a los adultos mayores por medio de la entrega de un subsidio económico para aquellos que se encuentran desamparados, que no cuentan con una pensión, o viven en la extrema pobreza. Requisitos para ingresar al programa Colombia mayor Dirigirse a la Dirección de Acción social Entregar los siguientes requisitos para verificación por parte de acción social Puntaje de Sisbén A, B hasta C1. No tener casa (esta información se verifica en RUAF). No tener pensión EPS subsidiada, en caso de no serlo se verificar el ingreso base de cotización. Mujeres se puede inscribir desde los 54 años y los hombres desde los 59 años. Tienen mayor prioridad personas mayores de 80 años, víctimas del conflicto armado y personas con discapacidad certificada por la Secretaría de Salud Municipal. La asignación de cupo depende del departamento para la prosperidad social del orden Nacional.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'programa', 'consiste', 'colombia', 'mayor'], 17);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Colombia Mayor?', 'Se realiza la inscripción en la Dirección de Acción social.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'colombia'], 18);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo el programa Colombia Mayor?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'colombia', 'mayor'], 19);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Colombia Mayor se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'colombia', 'mayor', 'puede'], 20);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede realizar la inscripción de forma presencial al programa Colombia Mayor?', 'Si, en la CRA 7 NO. 12-100. TEMA :PROGRAMA RENTA CIUDADANA', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'realizar', 'inscripción', 'forma'], 21);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Renta Ciudadana?', 'Renta Ciudadana Es un programa del DPS del Orden Nacional que busca contribuir a la superación de la pobreza, fomentar la movilidad social y apoyar la economía local, mediante la entrega de Transferencias Monetarias Condicionadas y No Condicionadas, siguiendo los principios de integralidad, efectividad y eficiencia. Su implementación se llevará a cabo de manera gradual y progresiva a través de diversas líneas de intervención. La focalización la realiza directamente el DPS y allega a la Dirección de Acción social una base de datos con las familias que han sido focalizadas. Requisitos para ingresar al programa Renta Ciudadana Población Sisbén e Indígena Sisbén de A a B4, si es población indígena se valida el certificado SIIC que expide el ministerio del Interior. Ser colombiano Esperar comunicado por parte del enlace Municipal Acercarse de manera personal', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'certificado', 'programa', 'consiste', 'renta', 'ciudadana', 'renta'], 22);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Renta Ciudadana Población Sisbén e Indígena?', 'No se realiza inscripción, pues el DPS selecciona a las familias beneficiarias y comunica a la Dirección de Acción Social.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'renta'], 23);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Renta Ciudadana Población Sisbén e Indígena?', 'No.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'renta'], 24);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción la inscripción al programa Renta Ciudadana Población Sisbén e Indígena se puede hacer en línea?', 'No.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'inscripción', 'renta', 'ciudadana'], 25);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer la inscripción al programa Renta Ciudadana Población Sisbén e Indígena de forma presencial?', 'No, el gobierno realiza la priorización desde las bases de datos. TEMA : PROGRAMAHABITANTE DE CALLE', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción', 'renta'], 26);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa habitante de Calle?', 'Es una estrategia que busca reducir los índices de habitabilidad para las personas que hacen de la calle su lugar de residencia permanente o transitoria, o de manera intermitente residen en diversos tipos de vivienda que combina la vivienda inadecuada, insegura y la calle, que hasta el momento se ha señalado bajo la denominación "personas en situación de calle", como la manera más clara de establecer el universo de personas hacia las cuales se deben adelantar acciones de inclusión e integración social debida a su extrema situación de exclusión socio habitacional. Requisitos para ingresar al programa habitante de Calle Dirigirse al centro transitorio de protección para que activen ruta de atención (Verificar procedencia, afiliación a Salud, Sisbén, ubicar red de apoyo, activar triage…)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'programa', 'apoyo', 'consiste', 'habitante', 'calle', 'estrategia'], 27);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa habitante de Calle?', 'No aplica, se realiza en Centro Transitorio de Protección Municipal', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'habitante'], 28);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo ingresar al programa habitante de Calle?', 'No,', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'ingresar', 'habitante'], 29);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa habitante de Calle se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'habitante', 'calle', 'puede'], 30);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer presencial la inscripción al programa habitante de Calle?', 'Si en el centro transitorio de protección de Chía, pues es necesaria la activación de la ruta de atención para las atenciones al habitante de calle. TEMA : PROGRAMARENTA JOVEN', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción'], 31);
