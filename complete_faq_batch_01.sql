-- Complete FAQ Integration Batch 1
-- Questions 49 to 98
-- Total questions in batch: 50

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtendré la respuesta?', 'Al término de quince días hábiles se habrá atendido su solicitud. Hay un(os) panal(es) de abejas en el lugar “x”, deseo que lo(s) retiren, ¿Qué debo hacer para tramitar mi solicitud? Por favor ingrese su nombre completo Número y tipo de identificación Ubicación del lugar Trasladaremos la petición al área encargada Encontré una zarigüeya en uno de los arbustos de mi jardín, ¿Qué hago para que la recojan? La misma respuesta que en la pregunta No. 2 Otras inquietudes que podrían tener los ciudadanos, vinculadas a las líneas de atención que maneja éste equipo de GRD: Quisiera que me agenden una capacitación en primeros auxilios, ¿Cuál es la ruta, los requisitos?', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'solicitud', 'cuánto', 'tiempo', 'obtendré', 'respuesta', 'término'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿A quién va dirigida la capacitación pregunta IA?: otra dependencia, institución educativa, colegio, otros. Elija una opción', 'Ingrese el número de personas a las cuáles va dirigida la capacitación Al término de quince días hábiles se dará respuesta a su solicitud.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['solicitud', 'quién', 'dirigida', 'capacitación', 'pregunta', 'otra'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué requisitos se deben cumplir para realizar un evento en el municipio?', 'Antes de la radicación del evento se deben adelantar estos requisitos: Debe presentar el plan de emergencia y contingencia <NAME_EMAIL> para que por parte de la Dirección Centro de Atención al Ciudadano se remita a bomberos y estos agenden la inspección, posteriormente remitir el informe de la inspección que adelantó Bomberos al correo <NAME_EMAIL> para que por parte de la Dirección Centro de Atención al Ciudadano se remita a la Secretaría de Gobierno – Gestión del Riesgo – Comité de Conocimiento y Prevención del Riesgo Para Eventos Masivos y No Masivos y así obtener el concepto favorable. Solicitar concepto favorable de la Secretaría de Salud diligenciando este link: https://forms.office.com/r/cuiu22nmVw Obtener concepto favorable de la Secretaría de Movilidad de Chía, radicando el Plan de Manejo de Tránsito (PMT) a <EMAIL> Enviar solicitud a la Policía Nacional para acompañamiento y control del evento en caso de ser necesario <NAME_EMAIL> Si el evento tiene recorrido, desfiles y/o similares, solicitar por escrito el permiso del evento y obtener respuesta de autorización por parte de la Secretaría de Movilidad. Si el evento es en sitio, presentar el Plan de Manejo de Tránsito (PMT) a la Secretaría de Tránsito y obtener el concepto favorable con anticipación. SI EN EL EVENTO SE LLEVARÁ A CABO RECORRIDO Y EVENTO EN SITIO SE DEBERÁN TRAMITAR LOS DOS PERMISOS – (permisos de los numerales 3 y 5). Cuando ya cuente con las anteriores autorizaciones, se radicará el trámite ante el Comité de Eventos como mínimo con un (1) mes antes de la realización del evento. La documentación que debes presentar es: Cédula del Representante Legal Plan de Emergencia y Contingencia Carta de intención del evento Concepto Favorable de la inspección adelantada por Bomberos Concepto Favorable de la inspección adelantada por Secretaría de Salud Concepto Favorable de la Inspección adelantada por Secretaría de Movilidad Presentar Paz y Salvo de pago de Derechos de Autor autorizadas por la Dirección Nacional de Derechos de Autor Póliza de Responsabilidad Civil Extracontractual que ampare la seguridad de las personas y de los elementos Anexar pantallazo de la solicitud a la Policía Nacional, en caso de requerirlo Si el evento es de espectáculos públicos, es decir, música, danza, circo sin animales, teatro, magia, debes presentar adicional: Certificados de contribución parafiscal Certificado de productor Ficha PULEP En caso de edificaciones nuevas, contar con un Concepto Técnico e Comportamiento Estructural y Funcional del escenario. NOTA: Se entenderá como radicado del evento un (1) solo correo que recopile toda la documentación requerida.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'certificado', 'solicitud', 'requisitos', 'deben', 'cumplir', 'para', 'realizar'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtengo la autorización?', 'El tiempo de respuesta para este trámite es de máximo quince (15) días.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuánto', 'tiempo', 'obtengo', 'autorización', 'tiempo'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué debo hacer para radicar la solicitud?', 'Una vez reunida toda la documentación requerida, puedes radicar la solicitud en la ventanilla o <NAME_EMAIL>', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['solicitud', 'debo', 'hacer', 'para', 'radicar'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene algún costo esta autorización?', 'NO TIENE COSTO', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene', 'algún', 'esta', 'autorización'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste la red cívica?', 'Es un cuerpo no armado de carácter civil, sin ánimo de lucro, constituido voluntariamente con el objeto de prestar apoyo para el cumplimiento de unas misiones específicas de carácter educativo, social y comunitario que realiza la Policía Nacional con el propósito de fortalecer las relaciones entre la policía y la comunidad, cuenta con más de 28 personas de apoyo disponibles las 24 horas del día, que se distribuyen en 3 turnos de 8 horas que están disponibles para el apoyo a la Policía Nacional Filosofía: Tiene como prioridad contribuir en la construcción y fortalecimiento del tejido social, los principios de convivencia y la cultura de la legalidad, a través de acciones preventivas, educativas y las enmarcadas en el ámbito social, desarrolladas por ciudadanos voluntarios convencidos de su importancia y aplicabilidad.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['apoyo', 'consiste', 'cívica', 'cuerpo', 'armado', 'carácter'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo puedo hacer parte de la red cívica de mayores?', 'Ser colombiano o extranjero nacionalizado Tener la mayoría de edad Acreditar título profesional Tener definida la situación militar No registrar antecedentes penales ni Disciplinarios No tener antecedentes en el registro nacional de medidas correctivas Acreditar afiliación al sistema de seguridad social Aprobar la verificación administrativa de la información practicada por la policía Nacional Superar la entrevista personal que realiza el respectivo oficial comandante y un psicólogo', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['registro', 'cómo', 'puedo', 'hacer', 'parte', 'cívica'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Existe una convocatoria para hacer parte de la cívica?', 'En el momento se encuentran abiertas las convocatorias para hacer parte de la RED CÍVICA, esto, hasta completar cupos habilitados', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['existe', 'convocatoria', 'para', 'hacer', 'parte'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la diferencia entre el servicio que presta la policía Nacional de chía y el que presta la red cívica?', 'La red cívica está conformada por particulares que prestan un apoyo administrativo y no tienen funciones policiales por ley, ni funciones de autoridad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la documentación que se requiere para hacer parte de la red cívica infantil?', '1. Ficha de inscripción- la policía facilita el formato 2. ⁠carta de autorización-la policía facilita el formato 3. ⁠fotocopia cédula de los padres 4. ⁠fotocopia de la tarjeta de identidad del niño(a) 5. ⁠fotocopia del carnet de vacunas 6. ⁠fotocopia del certificado de salud 7. ⁠fotocopia del certificado del médico general 8. ⁠fotocopia certificado estudiantil 9. ⁠2 fotos 3*4 fondo azul 10. ⁠todo en una carpeta blanca 4 alas', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['certificado', 'cuál', 'documentación', 'requiere', 'para', 'hacer'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se va a distribuir la red cívica en el municipio?', 'La RED CÍVICA estará disponible las 24 horas del día en 3 turnos que estarán haciendo rondas constantes en el municipio para velar por la seguridad del Municipio de Chía, se van a distribuir de la siguiente manera 2 Motorizados Cerca de Piedra y Fonquetá incluido los cerros 2 Motorizados Tíquiza y Fagua incluido los cerros 2 Motorizados Mercedes de Calahorra 2 Motorizados Bojacá 2 Motorizados Zona Centro 2 Motorizados La Balsa 2 Motorizados Samaria 2 Motorizados Fusca, Yerbabuena, corredor vial Centro Chía - Limite Fontanar y Sector Colombia - MC Donalds De esta manera la CIVICA estará brindando apoyo contante a la Policía del municipio', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['apoyo', 'cómo', 'distribuir', 'cívica', 'municipio', 'cívica'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'Acercándose a la secretaría de Gobierno del municipio de Chía que se encuentra ubicada en la cra 7#12-100, Chía, Cundinamarca', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'realiza', 'inscripción', 'acercándose', 'secretaría'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'La inscripción y/o vinculación a la RED CÍVICA del municipio NO tiene ningún costo', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene', 'inscripción', 'vinculación', 'cívica'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Si, en la cra 7#12-100, Chía, Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['presencial', 'puede', 'hace', 'chía', 'cundinamarca'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quiénes son los nuevos uniformados que están en el municipio?', 'El Alcalde Leonardo Donoso Ruiz en un trabajo constante por velar por la seguridad del Municipio de Chía y sus habitantes, gestionó con el Ministerio de Defensa Nacional, la llegada del grupo GOES quienes estarán acantonados en el municipio, en el CAI perimetral y estarán realizando operatividad constante, distribuidos con de acuerdo a las necesidades en seguridad de cada sector del Municipio de Chía', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quiénes', 'nuevos', 'uniformados', 'están', 'municipio'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En que se enfocará el GOES?', 'El GOES estará enfocado en labores referentes al orden público, delitos que tengan relación con lesiones personales, extorción, hurto, porte ilegal de estupefacientes, secuestro. Intimidación o engaño, abuso de menores, turismo sexual y contrabando, iintervenir hasta la llegada del Grupo Especial de Operaciones en situaciones de alto riesgo como incidentes con rehenes, entre otras.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['enfocará', 'goes', 'goes', 'estará', 'enfocado'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Familiares reportan que uno de sus miembros no ha regresado a la casa después de unos días, están preocupados por su ausencia, consultan en dónde podría estar?', 'Puede estar en el Centro de detención transitorio – (CDT) o puede estar en el Centro Transitorio de Protección (CTP) Cuál es la razón de encontrarse en el Centro de detención transitorio – (CDT) Puede estar detenido, por haber cometido presuntamente un delito.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['familiares', 'reportan', 'miembros', 'regresado', 'casa'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las razones por las cuales una persona puede encontrarse en el Centro Transitorio de Protección (CTP)?', 'Según lo estipulado en el Artículo 155 de la Ley 1801 de 2016, Código de Seguridad y Convivencia Ciudadana. Cuando la vida e integridad de una persona natural se encuentre en riesgo o peligro y no acepte la mediación policial corno mecanismo para la solución del desacuerdo, el personal uniformado de la Policía Nacional, podrá trasladarla para su protección en los siguientes casos: A. Cuando se encuentre inmerso en riña. B. Se encuentre deambulando en estado de indefensión. C. Padezca alteración del estado de conciencia por aspectos de orden mental. D. Se encuentre o aparente estar bajo efectos del consumo de bebidas alcohólicas o sustancias psicoactivas ilícitas o prohibidas y exteriorice comportamientos agresivos o temerarios E. Realice actividades peligrosas o de riesgo que pongan en peligro su vida o integridad, o la de terceros. h) Se encuentre en peligro de ser agredido.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'razones', 'cuales', 'persona', 'puede'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el trámite a seguir estando en el Centro de detención transitorio – (CDT)?', 'El policía que lo detuvo, está obligado a poner el informe a disposición del Fiscal de turno, para que este solicite al Centro de Servicios Judiciales la asignación de un juez de control de garantías, y lo llevaran para realización de las 3 audiencias – que son;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el trámite a seguir estando en el Centro Transitorio de Protección (CTP)?', 'Cumplidas 12 horas, a partir de que la policía lo llevó para protegerlo, debe permitírsele su salida, pero si la persona se comunica con un familiar o con su representante y este se acerca al CTP a fin de recogerlo, la Policía o miembro del Centro debe entregarlo. Cuáles Derechos se le deben garantizar a quienes se encuentren en el Centro de detención transitorio – (CDT) Tiene derecho a Guardar silencio, no auto incriminarse, comunicarse con un familiar, o con quien considere, tiene derecho a que se le nombre un defensor público y recibir buen trato.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'trámite', 'seguir', 'estando', 'centro'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles Derechos se le deben garantizar a quienes se encuentren en el Centro Transitorio de Protección (CTP)?', 'Tiene Derecho a recibir atención del grupo psicosocial, CTP (abogado, psicólogo y auxiliar de enfermería) Tiene Derecho a que el delegado del Ministerio Público (Personero en CTP) revise si la protección es legal o no, si no es legal el mismo personero debe ordenar al grupo del CTP que le permita su salida – ejm – debe estar incurso en una de las causales del art 155 y de la sentencia C310 DE 2023 Corte Constitucional.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'derechos', 'deben', 'garantizar', 'quienes'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo puedo verificar si se encuentra en el Centro de detención transitorio – (CDT) o en el Centro Transitorio de Protección (CTP)?', 'a. Consultando al observatorio de Seguridad y convivencia ciudadana (hora laboral) Carrera 7 – No 12 - 100 b. En hora no laboral se debe dirigir a la variante con segunda, calle 2 No 6 – 35 Avenida Variante, al lado de la sala de velación. – Edf azul. c. Diríjase al comando de policía del Curubito Calle 9 N 10- 63, cerca al parque central del Chía, ahí podrá solicitar información.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'puedo', 'verificar', 'encuentra', 'centro'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el tiempo de permanencia en el Centro de detención transitorio – (CDT)?', 'Máximo 36 horas contadas desde la captura hasta que termina la audiencia de legalización de la captura, luego de lo cual el policía debe alistar la carpeta para conducir al detenido al INPEC de la cárcel de mediana seguridad de Chocontá y/o a la Cárcel Modelo con esta última próximamente se formalizará convenio para tal fin.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'tiempo', 'permanencia', 'centro', 'detención'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué documentos debe entregar el Centro de detención transitorio – (CDT) al INPEC?', 'La carpeta de documentos debe contener: informe de captura lo hace le policía que lo retiene – acta de derechos de buen trato- tarjeta decadactilar- orden de medida de aseguramiento expedida por el juez- acta de la audiencia.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['documento', 'documentos', 'debe', 'entregar', 'centro', 'detención'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el tiempo de permanencia en el Centro Transitorio de Protección (CTP)?', 'Max 12 horas, si la auxiliar de enfermería considera que no está en sus capacidades, el grupo interdisciplinario lo debe remitir por intermedio de la policía al hospital o clínica más cercana.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'tiempo', 'permanencia', 'centro', 'transitorio'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué documentos debe contener la carpeta de la persona que ha estado en protección en el Centro transitorio de Protección (CTP)?', 'Formato de enfermería, Psicología, policía, abogado y personero en CTP.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['documento', 'documentos', 'debe', 'contener', 'carpeta', 'persona'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quién administra el Centro de detención transitorio – (CDT)?', 'El Director de Seguridad y Convivencia Ciudadana con el comandante de estación del Curubito', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quién', 'administra', 'centro', 'detención', 'transitorio'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quién administra el Centro transitorio de Protección (CTP)?', 'El Director de Seguridad y Convivencia Ciudadana con el líder profesional de planta de la Alcaldía Municipal CTP.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quién', 'administra', 'centro', 'transitorio', 'protección'], 12);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué norma se aplica en el Centro de detención transitorio – (CDT)?', 'Código Penal y de Procedimiento Penal Ley 906 de 2004 y Ley 600 de 2000', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['procedimiento', 'norma', 'aplica', 'centro', 'detención', 'transitorio'], 13);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué norma se aplica en el Centro transitorio de Protección (CTP)?', 'Código Nacional de Seguridad y Convivencia Ciudadana. Ley 1801 de 2016', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['norma', 'aplica', 'centro', 'transitorio', 'protección'], 14);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es El PISCC?', 'Es el instrumento de planeación estratégica para la gestión de la convivencia y la seguridad ciudadana territorial, en todos los departamentos, distritos y municipios del país, debe ser formulado y aprobado en los primeros seis meses de mandato de las nuevas administraciones locales (Artículos 201 y 205 Ley 1801 de 2016 – Modificado por el artículo 41 de la Ley 2197 de 2022). Contiene estrategias y líneas de acción que se deben convertir en programas y proyectos que tengan como finalidad dar solución, reducir o prevenir problemas priorizados de criminalidad, delincuencia, comportamientos contrarios a la convivencia, acceso a la justicia y factores de riesgo que afectan a las personas.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['programa', 'piscc', 'instrumento', 'planeación', 'estratégica', 'para'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la convivencia?', 'El Código Nacional de Seguridad y Convivencia Ciudadana – CNSCC, en su artículo 5, define la convivencia como la interacción pacífica, respetuosa y armónica entre las personas, con los bienes, y con el ambiente, en el marco del ordenamiento jurídico. Las categorías de convivencia son: seguridad, tranquilidad, ambiente y salud pública.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['convivencia', 'código', 'nacional', 'seguridad', 'convivencia'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la Seguridad?', 'El término seguridad se debe asumir como un concepto que ha evolucionado en el tiempo;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En dónde se encuentra ubicado El municipio de Chía?', 'Se encuentra ubicado en la provincia sabana centro del departamento de Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['dónde', 'encuentra', 'ubicado', 'municipio', 'chía'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuándo se conformó la provincia sabana?', 'La provincia sabana se creó mediante Ordenanza No. 023 de 1998 y actualizada con la Ordenanza No. 07 de 2001. inicialmente, dicha Provincia estuvo conformada por nueve (9) municipios: Cajicá, Chía, Cogua, Gachancipá, Nemocón, Sopo, Tabio, Tocancipá y Zipaquirá;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los límites del municipio de Chía?', 'El municipio de Chía limita al norte con el municipio de Cajicá;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuántas veredas está dividida administrativamente Chía?', 'En 9 Veredas más el Área Urbana.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuántas', 'veredas', 'está', 'dividida', 'administrativamente'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las Veredas que conforman el Municipio de Chía?', 'Fagua, Tiquiza, Fonquetá y Cerca de Piedra (Dentro de las veredas de Cerca de Piedra y Fonquetá se encuentra ubicado el Resguardo indígena Mhuysqa.), Bojacá, La Balsa, Samaria, Yerbabuena baja, Fusca y torca', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'veredas', 'conforman', 'municipio', 'chía'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se agrupan las veredas en Chía?', 'Este conjunto de veredas se agrupa en tres zonas: Zona occidental: Fagua, Tiquiza, Fonquetá y Cerca de Piedra. (ubicadas parcialmente sobre los cerros de la Valvanera). Zona central: Bojacá, La Balsa y Samaria. Zona oriental: Yerbabuena baja, Fusca y torca. (Ubicados parcialmente en la vereda del cerro yerbabuena alta). Dentro de las veredas de Cerca de Piedra y Fonquetá se encuentra ubicado el Resguardo indígena Mhuysqa.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'agrupan', 'veredas', 'chía', 'este'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la población de Chía 2024?', 'Hombres 79.147 (48.5 %) Mujeres 84.159 (51.5 %) guridad y Convivencia Ciudadana', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'población', 'chía', 'hombres', 'mujeres'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuánta es la Población rural en Chía 2024? Hombres', 'Rural 24.545 (15 %)', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuánta', 'población', 'rural', 'chía', 'hombres'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuánta es la Población Urbana en Chía 2024?', 'Urbana 138.761 (85 %) Cuanta es la Población Étnica en el Municipio de Chía 3524 indígenas 1056 Grupo étnico afrodescendientes y otras poblaciones étnicas Cuál es la Población migrante en el municipio de Chía Según la información suministrada por parte de la unidad de asuntos migratorios y estadísticos (Migración Colombia), con sede en chía Cundinamarca, entre el año 2021 - 2023 un total de 8.934 ciudadanos migrantes solicitaron permiso de permanencia con fines de radicarse dentro del municipio, estas cifras representan el 5,47% del total de la población;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la población en condición de discapacidad?', 'Solicitantes Certificados de discapacidad 1.758 Con certificado de discapacidad 1.324', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['certificado', 'cuál', 'población', 'condición', 'discapacidad', 'solicitantes'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los delitos priorizados de atención en el municipio de Chía?', 'Homicidios, delitos sexuales, extorsión, consumo de sustancias psicoativas, hurto en todas sus modalidades, violencia intrafamiliar y riñas', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'delitos', 'priorizados', 'atención', 'municipio'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la red cívica?', 'SERVICIO DE VIGILANCIA (DEFENSA CONTROLADA) Fortalecen el cuidado en parques, centro de prácticas deportivas e instituciones educativas según programación. VEHÍCULOS DE RESPUESTAS Se divide el municipio en 2 zonas, 2 vehículos, cada uno apoya 4 cuadrantes. VIGILANCIA SECTORIZADA Se divide el municipio en 8 cuadrantes, Patrullaran 16 motorizados por turno de 8 horas, 24 horas al día, se incrementa el recurso humano para mayor cobertura.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['programa', 'servicio', 'cívica', 'vigilancia', 'defensa', 'controlada'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los sectores en los que se realiza la vigilancia motorizada en Chía?', 'Operación: 16 motos, 3 turnos de 8 horas 2 Motorizados Cerca de Piedra y Fonquetá incluido los cerros 2 Motorizados Tíquiza y Fagua incluido los cerros 2 Motorizados Mercedes de Calahorra 2 Motorizados Bojacá 2 Motorizados Zona Centro 2 Motorizados La Balsa 2 Motorizados Samaria 2 Motorizados Fusca, Yerbabuena, corredor vial Centro Chía - Limite Fontanar y Sector Colombia - MC Donalds', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'sectores', 'realiza', 'vigilancia', 'motorizada'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los perímetros de apoyo de vehículos de la red Cívica?', 'Se divide en dos zonas, así: Zona 1: Vereda Fagua, Vereda Tiquiza, Vereda Fonqueta, Vereda Cerca de Piedra, Perimetro urbano principal Zona 2: Vereda Bojacá, Mercedes de Calahorra, Vereda Yerbabuena, Vereda Fusca, Vereda la Balsa, Vereda Samaria', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['apoyo', 'cuáles', 'perímetros', 'vehículos', 'cívica'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la patrulla Purpura?', 'Es una estrategia que busca proteger a las mujeres de Chía de actos de violencia de género y violencia intrafamiliar, garantizando sus derechos.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['patrulla', 'purpura', 'estrategia', 'busca', 'proteger'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la misión de la patrulla Purpura?', 'Su misión proteger y mitigar cualquier acto de violencia en contra de las mujeres.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'misión', 'patrulla', 'purpura', 'misión'], 5);
