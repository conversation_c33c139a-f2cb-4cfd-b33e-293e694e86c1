"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/services/faqService.ts":
/*!************************************!*\
  !*** ./lib/services/faqService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n/* harmony import */ var _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n\n\n/**\n * Servicio para gestionar preguntas frecuentes municipales\n * Conectado con Supabase usando búsqueda de texto completo en español\n */ class FAQService {\n    static getInstance() {\n        if (!FAQService.instance) {\n            FAQService.instance = new FAQService();\n        }\n        return FAQService.instance;\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQItem\n   */ mapFAQFromDB(faqRow, themeName) {\n        return {\n            id: faqRow.id,\n            question: faqRow.question,\n            answer: faqRow.answer,\n            theme: themeName || \"\",\n            themeId: faqRow.theme_id,\n            keywords: faqRow.keywords || [],\n            displayOrder: faqRow.display_order,\n            popularityScore: faqRow.popularity_score || 0,\n            viewCount: faqRow.view_count || 0,\n            helpfulVotes: faqRow.helpful_votes || 0,\n            unhelpfulVotes: faqRow.unhelpful_votes || 0,\n            lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || \"\")\n        };\n    }\n    /**\n   * Convertir datos de base de datos a interfaz FAQTheme\n   */ mapThemeFromDB(themeRow) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return {\n            id: themeRow.id,\n            name: themeRow.name,\n            description: themeRow.description,\n            displayOrder: themeRow.display_order,\n            dependencyId: themeRow.dependency_id,\n            subdependencyId: themeRow.subdependency_id,\n            count\n        };\n    }\n    /**\n   * Obtener todos los temas desde Supabase\n   */ async getThemes() {\n        try {\n            // Verificar cache\n            const now = Date.now();\n            if (now - this.lastCacheUpdate < this.cacheExpiry && this.themesCache.size > 0) {\n                return Array.from(this.themesCache.values()).sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n            }\n            // Obtener temas activos\n            const { data: themesData, error: themesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_themes\").select(\"*\").eq(\"is_active\", true).order(\"display_order\");\n            if (themesError) {\n                console.error(\"Error fetching FAQ themes:\", themesError);\n                return [];\n            }\n            // Obtener conteo real de FAQs por tema\n            const { data: faqCounts, error: countError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"theme_id\").eq(\"is_active\", true);\n            if (countError) {\n                console.error(\"Error fetching FAQ counts:\", countError);\n            }\n            // Crear mapa de conteos\n            const countMap = new Map();\n            faqCounts === null || faqCounts === void 0 ? void 0 : faqCounts.forEach((faq)=>{\n                if (faq.theme_id) {\n                    countMap.set(faq.theme_id, (countMap.get(faq.theme_id) || 0) + 1);\n                }\n            });\n            // Mapear y cachear temas\n            const themes = (themesData === null || themesData === void 0 ? void 0 : themesData.map((theme)=>this.mapThemeFromDB(theme, countMap.get(theme.id) || 0))) || [];\n            // Actualizar cache\n            this.themesCache.clear();\n            themes.forEach((theme)=>this.themesCache.set(theme.id, theme));\n            this.lastCacheUpdate = now;\n            return themes.sort((a, b)=>(a.displayOrder || 0) - (b.displayOrder || 0));\n        } catch (error) {\n            console.error(\"Error in getThemes:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQs por tema desde Supabase\n   */ async getFAQsByTheme(themeId, limit) {\n        try {\n            let query = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"\\n          *,\\n          faq_themes!inner(name)\\n        \").eq(\"theme_id\", themeId).eq(\"is_active\", true).order(\"popularity_score\", {\n                ascending: false\n            });\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching FAQs by theme:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_themes;\n                return this.mapFAQFromDB(faq, (_faq_faq_themes = faq.faq_themes) === null || _faq_faq_themes === void 0 ? void 0 : _faq_faq_themes.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getFAQsByTheme:\", error);\n            return [];\n        }\n    }\n    /**\n   * Buscar FAQs usando búsqueda de texto completo en español\n   */ async searchFAQs(query) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const startTime = Date.now();\n        const { theme, dependencyId, subdependencyId, limit = 10, includeKeywords = true } = options;\n        if (!query.trim()) {\n            return [];\n        }\n        try {\n            const searchTerm = query.trim();\n            // Construir query base con búsqueda de texto completo\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"\\n          *,\\n          faq_themes!inner(name, dependency_id, subdependency_id)\\n        \").eq(\"is_active\", true);\n            // Filtrar por tema si se especifica\n            if (theme) {\n                supabaseQuery = supabaseQuery.eq(\"theme_id\", theme);\n            }\n            // Filtrar por dependencia si se especifica\n            if (dependencyId) {\n                supabaseQuery = supabaseQuery.eq(\"faq_themes.dependency_id\", dependencyId);\n            }\n            // Filtrar por subdependencia si se especifica\n            if (subdependencyId) {\n                supabaseQuery = supabaseQuery.eq(\"faq_themes.subdependency_id\", subdependencyId);\n            }\n            // Usar búsqueda de texto completo en español con tsvector\n            const { data, error } = await supabaseQuery.textSearch(\"search_vector\", \"'\".concat(searchTerm, \"'\"), {\n                type: \"plainto\",\n                config: \"spanish\"\n            }).order(\"popularity_score\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error searching FAQs:\", error);\n                // Fallback a búsqueda simple si falla la búsqueda de texto completo\n                return this.fallbackSearch(searchTerm, options);\n            }\n            // Mapear resultados\n            const results = (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_themes;\n                return this.mapFAQFromDB(faq, (_faq_faq_themes = faq.faq_themes) === null || _faq_faq_themes === void 0 ? void 0 : _faq_faq_themes.name);\n            })) || [];\n            const responseTime = Date.now() - startTime;\n            // Registrar analytics\n            if (results.length === 0) {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackNoResults(query, theme);\n            } else {\n                _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackSearch(query, results.length, responseTime, theme);\n            }\n            return results;\n        } catch (error) {\n            console.error(\"Error in searchFAQs:\", error);\n            return this.fallbackSearch(query, options);\n        }\n    }\n    /**\n   * Búsqueda de respaldo usando ILIKE\n   */ async fallbackSearch(query, options) {\n        try {\n            const { theme, limit = 10 } = options;\n            const searchTerm = query.toLowerCase().trim();\n            let supabaseQuery = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"municipal_faqs\").select(\"\\n          *,\\n          faq_themes!inner(name)\\n        \").eq(\"is_active\", true);\n            if (theme) {\n                supabaseQuery = supabaseQuery.eq(\"theme_id\", theme);\n            }\n            const { data, error } = await supabaseQuery.or(\"question.ilike.%\".concat(searchTerm, \"%,answer.ilike.%\").concat(searchTerm, \"%\")).order(\"popularity_score\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error in fallback search:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_themes;\n                return this.mapFAQFromDB(faq, (_faq_faq_themes = faq.faq_themes) === null || _faq_faq_themes === void 0 ? void 0 : _faq_faq_themes.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in fallbackSearch:\", error);\n            return [];\n        }\n    }\n    /**\n   * Calcular puntuación de relevancia para FAQs municipales\n   */ calculateRelevanceScore(faq, searchTerm) {\n        let score = 0;\n        const term = searchTerm.toLowerCase();\n        // Coincidencia exacta en pregunta (peso alto)\n        if (faq.question.toLowerCase().includes(term)) {\n            score += 100;\n        }\n        // Coincidencia en respuesta (peso medio)\n        if (faq.answer.toLowerCase().includes(term)) {\n            score += 50;\n        }\n        // Coincidencia en keywords (peso medio)\n        faq.keywords.forEach((keyword)=>{\n            if (keyword.toLowerCase().includes(term)) {\n                score += 30;\n            }\n        });\n        // Popularidad (peso bajo)\n        score += faq.popularityScore * 0.1;\n        return score;\n    }\n    /**\n   * Obtener FAQs más populares desde Supabase\n   */ async getPopularFAQs() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        try {\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"is_active\", true).order(\"popularity\", {\n                ascending: false\n            }).limit(limit);\n            if (error) {\n                console.error(\"Error fetching popular FAQs:\", error);\n                return [];\n            }\n            return (data === null || data === void 0 ? void 0 : data.map((faq)=>{\n                var _faq_faq_categories;\n                return this.mapFAQFromDB(faq, (_faq_faq_categories = faq.faq_categories) === null || _faq_faq_categories === void 0 ? void 0 : _faq_faq_categories.name);\n            })) || [];\n        } catch (error) {\n            console.error(\"Error in getPopularFAQs:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtener FAQ por ID desde Supabase\n   */ async getFAQById(id) {\n        try {\n            var _data_faq_categories;\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"\\n          *,\\n          faq_categories!inner(name)\\n        \").eq(\"id\", id).eq(\"is_active\", true).single();\n            if (error) {\n                console.error(\"Error fetching FAQ by ID:\", error);\n                return null;\n            }\n            const faq = this.mapFAQFromDB(data, (_data_faq_categories = data.faq_categories) === null || _data_faq_categories === void 0 ? void 0 : _data_faq_categories.name);\n            // Registrar visualización y actualizar contador\n            _faqAnalytics__WEBPACK_IMPORTED_MODULE_1__[\"default\"].trackFAQView(faq.id, faq.question);\n            // Incrementar view_count en la base de datos\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").update({\n                view_count: (data.view_count || 0) + 1\n            }).eq(\"id\", id);\n            return faq;\n        } catch (error) {\n            console.error(\"Error in getFAQById:\", error);\n            return null;\n        }\n    }\n    /**\n   * Obtener estadísticas del FAQ desde Supabase\n   */ async getFAQStats() {\n        try {\n            // Obtener estadísticas de FAQs\n            const { data: faqStats, error: faqError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faqs\").select(\"popularity, category_id\").eq(\"is_active\", true);\n            if (faqError) {\n                console.error(\"Error fetching FAQ stats:\", faqError);\n                return {\n                    totalFAQs: 0,\n                    totalCategories: 0,\n                    averagePopularity: 0,\n                    mostPopularCategory: \"\"\n                };\n            }\n            // Obtener estadísticas de categorías directamente\n            const { data: categoryStats, error: categoryError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"faq_categories\").select(\"id, name\").eq(\"is_active\", true);\n            if (categoryError) {\n                console.error(\"Error fetching category stats:\", categoryError);\n            }\n            // Calcular estadísticas\n            const totalFAQs = (faqStats === null || faqStats === void 0 ? void 0 : faqStats.length) || 0;\n            const totalCategories = (categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.length) || 0;\n            const averagePopularity = totalFAQs > 0 ? Math.round(faqStats.reduce((sum, faq)=>sum + (faq.popularity || 0), 0) / totalFAQs) : 0;\n            // Calcular categoría más popular sin llamar a getCategories()\n            const categoryCount = new Map();\n            faqStats === null || faqStats === void 0 ? void 0 : faqStats.forEach((faq)=>{\n                if (faq.category_id) {\n                    categoryCount.set(faq.category_id, (categoryCount.get(faq.category_id) || 0) + 1);\n                }\n            });\n            let mostPopularCategory = \"\";\n            let maxCount = 0;\n            categoryCount.forEach((count, categoryId)=>{\n                if (count > maxCount) {\n                    maxCount = count;\n                    const category = categoryStats === null || categoryStats === void 0 ? void 0 : categoryStats.find((cat)=>cat.id === categoryId);\n                    mostPopularCategory = (category === null || category === void 0 ? void 0 : category.name) || \"\";\n                }\n            });\n            return {\n                totalFAQs,\n                totalCategories,\n                averagePopularity,\n                mostPopularCategory\n            };\n        } catch (error) {\n            console.error(\"Error in getFAQStats:\", error);\n            return {\n                totalFAQs: 0,\n                totalCategories: 0,\n                averagePopularity: 0,\n                mostPopularCategory: \"\"\n            };\n        }\n    }\n    constructor(){\n        this.themesCache = new Map();\n        this.cacheExpiry = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.lastCacheUpdate = 0;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/faqService.ts\n"));

/***/ })

});