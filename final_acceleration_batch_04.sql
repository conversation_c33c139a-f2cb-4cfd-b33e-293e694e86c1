-- Final Acceleration Batch 4
-- Questions 286 to 334
-- Total questions in batch: 49

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al Programa Forma TIC de la Red de Bibliotecas?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'forma'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al Programa Forma TIC de la Red de Bibliotecas se puede hacer en línea?', 'Si, <EMAIL> Servicios de Red de bibliotecas SERVICIOS | Red de Bibliotecas Públicas del Municipio de Chía – RBPMC (wordpress.com) SERVICIOS – Red de Bibliotecas Públicas Municipio de Chía (chia-cundinamarca.gov.co)', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['programa', 'servicio', 'inscripción', 'forma', 'bibliotecas', 'puede'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al Programa Forma TIC de la Red de Bibliotecas se puede hacer presencial?', 'Si, En la Biblioteca HOQABIGA Carrera 7 No. 15-51 Chía;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa servicio social para estudiantes de grado 10 y 11, en las bibliotecas públicas del municipio de chía?', 'El Servicio social obligatorio que deben realizar los estudiantes en los grados 10 u 11, se puede realizar en La Red de Bibliotecas Públicas del municipio. Fundamentada en la Resolución 4210 de 1996, es para que los estudiantes de las instituciones educativas oficiales y privadas del municipio que estén cursando 10° y 11° realicen su Servicio Social Estudiantil Obligatorio SSEO;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía?', 'La inscripción se realiza de manera presencial en las instalaciones a la dirección de cultura', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'servicio', 'cómo', 'realiza', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'servicio', 'tiene', 'inscripción'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea la inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'servicio', 'inscripción', 'puede', 'hacer', 'línea', 'inscripción'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía Se puede hace presencial?', 'Si, Carrera 7 No. 15-51 Chía – Chía, Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'servicio', 'inscripción', 'social', 'obligatorio'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa escuela De Formación Artística De Chía?', 'Consiste en un programa de formación académica a la institución de educación para el trabajo y el desarrollo humano denominado ESCUELA DE FORMACION ARTISTICA Y CULTURAL DE CHIA que tiene cubrimiento en todo el municipio (juntas de acción comunal, conjuntos residenciales, fundaciones, CDI e Instituciones Educativas Oficiales);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa escuela De Formación Artística De Chía?', 'La inscripción se realiza de forma presencial en las instalaciones de la Biblioteca 2 piso con el funcionario Iván Manrique.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'escuela'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa escuela De Formación Artística De Chía?', 'Si, depende de la Clasificación del Sisbén para los puntajes de C15-C16 pagan $34.000 y C17 A D20 pagan $68.000, D21 o los que no tienen Sisbén pagan $171.000 para el año 2024.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'escuela'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa escuela De Formación Artística De Chía se puede hacer en línea?', 'No, pero próximamente se habilitará link.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['programa', 'inscripción', 'escuela', 'formación', 'artística'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa escuela De Formación Artística De Chía Se puede hace presencial?', 'Si, Carrera 7 No. 15-51 Chía Segundo piso– chía, Cundinamarca – Colombia.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['presencial', 'programa', 'inscripción', 'escuela', 'formación', 'artística'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste Beneficios Económicos Periódicos para artistas y gestores culturales?', 'Es un programa del MINISTERIO DE CULTURA donde se busca ayudar y beneficiar a artistas y gestores culturales que por su oficio nunca lograron acceder a una pensión. La alcaldía de Chía en cabeza de la dirección de cultura coordina la convocatoria de recepción de documentos de las personas interesadas en acceder a este beneficio, así mismo es la, entidad encargada de girar el recurso, este proviene del recaudo correspondiente al 10% de la estampilla pro-cultura. Una vez recopilada la lista de postulados se remiten al ministerio de cultura y este es quien hace la lista de priorización de beneficiaros. El valor del aporte entregado a los artistas o gestores culturales beneficiados corresponde al 30% de cada mes del salario mínimo y llega cada dos meses. Requisitos para acceder al programa Beneficios Económicos Periódicos para artistas y gestores culturales 1. Ser colombiano. 2. Tener mínimo 62 años de edad si es hombre y 57 años de edad si es mujer. 3. Residir durante los últimos diez (10) años en el territorio nacional. 4. Percibir ingresos inferiores a un (1) Salario Mínimo Mensual Legal Vigente (SMMLV). 5. Que se acredite, a través del Ministerio de Cultura, la condición de gestor o creador cultural, de acuerdo con los requisitos que esa cartera ministerial determine para tal fin. 6. no tener pensión 7. certificado de residencia expedido por la oficina asesora jurídica que acredite su residencia en el municipio de chía. 8. en el sistema de salud debe figurar como beneficiario o subsidiado (si aparece como cotizante, no queda beneficiado).', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'documento', 'certificado', 'programa', 'beneficio', 'consiste', 'beneficios', 'económicos'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales?', 'La recepción de documentos está abierta durante todo el tiempo acercándose de manera presencial a la Dirección de cultura. La Dirección de cultura informara al interesado la lista de documentos que debe reunir y le entregara el formato establecido por el ministerio de cultura.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['documento', 'presencial', 'programa', 'beneficio', 'cómo', 'realiza', 'inscripción', 'beneficios'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'beneficio', 'tiene', 'inscripción', 'beneficios'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'beneficio', 'inscripción', 'beneficios', 'económicos', 'periódicos'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales Se puede hace presencial?', 'Si, Carrera 7 No. 15-51 Chía Segundo piso– chía, Cundinamarca – Colombia. Más información en: https://sifo.mincultura.gov.co/home', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'beneficio', 'inscripción', 'beneficios', 'económicos', 'periódicos'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa portafolio municipal de estímulos (PME)?', 'Es un programa a través de la cual la dirección de cultura se financian iniciativas culturales que cumplan con alguna de las siguientes líneas de acción: creación, formación, circulación, y que contribuyan a la reactivación cultural del territorio a través de procesos artísticos y culturales, o a la reactivación económica del sector cultural. Las líneas de acción del portafolio municipal de estímulos se dividen en tres categorías: Creación. Fortalecimiento de los procesos para la generación de nuevos productos, bienes y/o servicios artísticos y culturales mediante ejercicios de exploración e innovación. Circulación. Desarrollo de procesos, proyectos y actividades que buscan fortalecer la oferta cultural en el territorio y la divulgación de expresiones y productos artísticos a nivel departamental, nacional y/o internacional. Formación. Fortalecimiento de los espacios de transmisión e intercambio de saberes y conocimientos alrededor de las prácticas culturales. Podrán ser dirigidos a los artistas o a las comunidades. Requisitos para acceder al programa portafolio municipal de estímulos (PME) 1. Personas naturales, colombianas o extranjeras, mayores de dieciocho (18) años, con residencia y domicilio en el municipio de Chía. Requisitos: Si es ciudadano colombiano, ser mayor de dieciocho (18) años, con residencia y domicilio artístico o cultural el municipio de Chía en los últimos dos (2) años. Si es ciudadano extranjero, ser mayor de dieciocho (18) años, con residencia continua en el municipio de Chía en los últimos cinco (5) años Ejercer como actividad económica la creación, circulación, investigación, y formación artística y cultural, evidenciando lo anterior en las actividades económicas registradas en el Registro Único Tributario RUT Tener trayectoria en la realización y ejecución de proyectos, programas, estrategias y/o actividades en las prácticas artísticas y culturales, reflejando lo anterior en los soportes de las Hojas de Vida. Registro Único Nacional de Agentes Culturales - Soy Cultura, dando cumplimiento a lo establecido en el artículo 18 de la ley 2070 de 2020. estar inscritos en el Sistema de Información Cultural de Chía. Cédula de ciudadanía o extranjería Certificado de Residencia Registro único tributario RUT Actualizado Formato Acreditación Trayectoria y experiencia Certificación bancaria activa a nombre del proponente 2. Personas jurídicas de naturaleza privada constituidas con antelación mínima de tres (3) años a la apertura, y con sede, domicilio, y trabajo artístico o cultural relacionado con cultura y/o patrimonio en el Municipio. Debe ser de naturaleza privada, con o sin ánimo de lucro, con domicilio y sede principal en la ciudad de Chía. Deberá estar inscrita y registrada ante la entidad competente, con fecha anterior a la fecha máxima de recepción de presentación de propuestas. Ejercer como actividad económica la creación, circulación, investigación, y formación artística y cultural, evidenciando lo anterior en las actividades económicas registradas en el Registro Único Tributario RUT. Tener trayectoria en la realización y ejecución de proyectos, programas, estrategias y/o actividades en las prácticas artísticas y culturales, reflejando lo anterior en los soportes de las Hojas de Vida. Registro Único Nacional de Agentes Culturales - Soy Cultura, dando cumplimiento a lo establecido en el artículo 18 de la 2070. Estar inscritos en el Sistema de Información Cultural de Chía. Certificación de existencia y representación legal de la organización aspirante otorgada por la entidad competente Cédula de ciudadanía o extranjería Copia del Registro Único Tributario de la organización Certificación bancaria de cuenta activa.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA CULTURA'), 
 ARRAY['requisito', 'certificado', 'registro', 'programa', 'servicio', 'consiste', 'portafolio', 'municipal'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa portafolio municipal de estímulos (PME)?', 'Estar pendiente de la convocatoria y en ella se dispone de un formulario de inscripción web.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA CULTURA'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'portafolio'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa portafolio municipal de estímulos (PME)?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA CULTURA'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'portafolio'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa portafolio municipal de estímulos (PME) se puede hacer en línea?', 'Si en el link asignado en la página https://casadelaculturachia.gov.co/', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA CULTURA'), 
 ARRAY['programa', 'inscripción', 'portafolio', 'municipal', 'estímulos'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa portafolio municipal de estímulos (PME) se puede hacer presencial?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA CULTURA'), 
 ARRAY['presencial', 'programa', 'inscripción', 'portafolio', 'municipal', 'estímulos'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'En una estrategia de apoyos educativos para ingresar a Instituciones de Educación Superior, El programa está diseñado para apoyar el bienestar de los estudiantes residentes en el Municipio de Chía, cuyo objeto es contribuir de manera conjunta es satisfacer la demanda de educación superior de nuestros ciudadanos. El FOES es el Fondo para el Fomento de la Educación Superior del Municipio de Chía. Beneficiar estudiantes de educación superior del municipio a través de la adjudicación de créditos, apoyos y subsidios dicho fondo constituye presupuestalmente unidad de caja. cuenta con autonomía patrimonial. es administrado por un Comité Administrativo. Requisitos para ingresar al programa Requisitos Estudiantes Nuevos Apoyos Educativos y Subsidio de Sostenimiento Certificado de Residencia Expedido por la JAC. Certificado Consulta SISBÉN. Certificado de Notas Bachiller. Documento de Identidad. Acta y Diploma de Grado de Bachiller. Resultado Pruebas Saber 11. Recibo de Matrícula. Requisitos Estudiantes Antiguos Apoyos Educativos y Subsidio de Sostenimiento Certificado de Residencia Expedido por la JAC. Certificado Consulta SISBEN. Certificado de Notas Semestre Anterior. Documento de Identidad. Recibo de Matrícula. Requisitos Crédito Educativo Certificado de Residencia Expedido por la JAC Certificado Consulta SISBÉN. Recibo de Matrícula. Documento de Identidad Solicitante. Documento de Identidad Codeudor. Autorización Consulta en Centrales de Riesgo Solicitante y Codeudor. Certificación Laboral, y Certificado de Ingresos y Retenciones del codeudor si es empleado. Extra juicio manifestando los ingresos mensuales del codeudor si es trabajador independiente. Declaración de Renta o Certificado de no Declarante si es trabajador independiente. No tener deudas con el municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['requisito', 'documento', 'certificado', 'programa', 'apoyo', 'consiste', 'estrategia', 'apoyos'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al FOES?', 'Debe inscribirse en la Ventanilla de Tramites / Opción FOES, se requiere en PDF los documentos señalados en los requisitos, se realizan 2 convocatorias en el año, la primera entre abril y mayo, la segunda entre octubre y noviembre, la apertura de la convocatoria se publica en la Página Web Oficial de la Alcaldía de Chía', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['tramite', 'requisito', 'documento', 'cómo', 'realiza', 'inscripción', 'foes', 'debe'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa FOES?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'foes'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa FOES se puede hacer en línea?', 'Si, el tramite es únicamente virtual en el link publicado en la página WEB', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['tramite', 'virtual', 'programa', 'inscripción', 'foes', 'puede', 'hacer'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa FOES Se puede hace presencial?', 'No, el tramite es únicamente virtual', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['tramite', 'presencial', 'virtual', 'programa', 'inscripción', 'foes', 'puede', 'hace'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'Proceso de afiliación al Sistema General de Seguridad Social en Salud a través del régimen contributivo para quienes tiene capacidad de pago y a través del régimen subsidiado para la población pobre y vulnerable y sin capacidad de pago. Requisitos para el proceso de afiliación en el régimen contributivo Estar debidamente identificados, tanto el cotizante como su grupo familiar, con la documentación necesaria atendiendo el rango de edad. Tener capacidad de pago (trabajador dependiente o independiente, pensionado, rentista, servidores públicos) No estar afiliado a régimen especial o de excepción (magisterio, ferrocarriles nacionales, Ecopetrol, sanidad militar, sanidad de la policía) ​', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'consiste', 'proceso', 'afiliación', 'sistema', 'general'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la afiliación al Régimen Contributivo?', 'Se efectúa con la inscripción a una sola Entidad Promotora de Salud (EPS), mediante la suscripción del Formulario Único de Afiliación y Registro de Novedades al Sistema General de Seguridad Social en Salud. Tanto la afiliación como las novedades de traslado y de movilidad se realizan en el formulario adoptado por el Ministerio de Salud y Protección Social. En el caso de los cotizantes dependientes, el formulario deberá ser suscrito también por el empleador. El empleado escoge la EPS de su elección y se lo comunica a su empleador junto con los datos de su familia. Igualmente sucede con los aprendices en etapa lectiva y productiva. En el caso del pensionado, debe informar al fondo de pensiones, cuál es la EPS a la que este deberá entregar su cotización, que podrá ser la misma a la que venía afiliado u otra en caso de que haya decidido cambiarse, siempre y cuando, cumpla los requisitos exigidos para el efecto. El fondo de pensiones debe suscribir el Formulario Único de Afiliación y Registro de Novedades para afiliarlo y pagar la cotización mensual a la EPS seleccionada. El trabajador independiente debe afiliarse por su cuenta a la EPS de su elección, inscribiéndose en la misma, diligenciando el formulario respectivo y luego hacer los pagos mensuales a través de la planilla integrada. Requisitos para el proceso de afiliación en el régimen subsidiado Estar debidamente identificados, tanto el cabeza de familia como su grupo familiar, con la documentación necesaria atendiendo el rango de edad. Estar caracterizado a través de la encuesta SISBEN vigente (Sisben IV) en el Municipio de residencia o estar registrado en algún listado censal (personas en condición de desplazamiento, población infantil abandonada a cargo del ICBF, menores desvinculados del conflicto armado, comunidades indígenas;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la afiliación al Régimen subsidiado?', 'El cabeza de familia puede acercarse a cualquiera de las EPS del régimen subsidiado que opere en el Municipio de residencia y realizar la inscripción a la entidad de su preferencia, mediante la suscripción del Formulario Único de Afiliación y Registro de Novedades al Sistema General de Seguridad Social en Salud. Tanto la afiliación como las novedades de traslado y de movilidad se realizan en el formulario adoptado por el Ministerio de Salud y Protección Social. El cabeza de familia puede acercarse a la secretaría de salud de su Municipio de residencia y solicitar una afiliación de oficio a través del Sistema de Afiliación Transaccional (SAT)', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['registro', 'cómo', 'realiza', 'afiliación', 'régimen', 'subsidiado'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'Si. Cada ciudadano, cotizante (régimen contributivo) o cabeza de familia (régimen subsidiado), puede realizar la autogestión de la afiliación al Sistema General de Seguridad Social en Salud suyo como de su núcleo familiar a través del Sistema de Afiliación Transaccional (SAT) en el link https://miseguridadsocial.gov.co/ creando un usuario y contraseña propios para acceder y gestionar sus procesos de aseguramiento en salud.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea', 'cada'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Si, en la Carrera 10 No. 8-74, Centro Comercial el Curubito, Chía, Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['presencial', 'puede', 'hace', 'carrera', 'centro'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'Gestionar y brindar orientación e información de manera oportuna garantizando la respuesta efectiva a las PQRS relacionadas con el aseguramiento y la prestación de servicios de salud interpuestas por los ciudadanos. Comienza con la radiación del requerimiento por parte del ciudadano desde donde se realiza análisis, verificación, seguimiento y termina con la respuesta oportuna a la solicitud planteada en temas de salud. Requisitos para el proceso Contar con soportes documentales o fácticos que respalden la PQRS Como se realiza el proceso El ciudadano puede acercarse a la oficina “Punto de Atención al ciudadano y orientación” –PACO– y radicar documento escrito en el cual detalla y soporta su PQRS. Allí se le asigna un número consecutivo de radicación con el cual puede hacer seguimiento El ciudadano puede acercarse a la oficina de la Secretaría de Salud de Chía y presentar documento escrito en el cual detalla y soporta su PQRS. Allí se escanean los documentos y se le devuelven inmediatamente al ciudadano junto con una firma de recibido de parte del servidor público que atiende la solicitud. El ciudadano puede enviar un correo electrónico a <NAME_EMAIL> en el cual detalla y soporta su PQRS. En respuesta recibirá un número consecutivo de radicación con el cual puede hacer seguimiento.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'documento', 'solicitud', 'servicio', 'consiste', 'gestionar', 'brindar', 'orientación'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['costo', 'tiene'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'Si. A través de la cuenta electrónica <EMAIL> en el cual detalla y soporta su PQRS.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea', 'través'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Si: 1. En la Carrera 10 No. 8-74, Centro Comercial el Curubito, Chía, Cundinamarca;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa de emprendimiento?', 'Acompañamiento a una idea de negocio, mediante formación empresarial, espacios comerciales, capacitaciones y transferencias de conocimiento. Requisitos para ingresar al programa de emprendimiento Ser Residente del Municipio de Chía​ Cultivar, producir, transformar y/o generar servicios en Chía​ Presentar muestra de los productos, con el fin de ser evaluados respecto a calidad, empaque, presentación, logos, marca, diseño y sabor (para el caso de alimentos) ​ Presentar un producto innovador o con factor de diferenciación frente a la competencia​ Tener certificado de manipulación de alimentos vigente (para el caso de alimentos) ​ ​Asistir a las capacitaciones y/o transferencias de conocimiento programados por la Dirección De Desarrollo Agropecuario y Empresarial.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['requisito', 'certificado', 'programa', 'servicio', 'consiste', 'emprendimiento', 'acompañamiento', 'idea'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa de emprendimiento?', 'El emprendedor debe solicitar a través de los canales de la DDAE presencial, WSP o a través <NAME_EMAIL>, informando de su intención de inscribirse al programa, indicando cuál es su emprendimiento y sus datos de contacto. La Secretaría Para el Desarrollo Económico responderá el correo indicando la fecha y hora en que pueden atender presencialmente al emprendedor con el fin de conocer su producto o servicio.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['presencial', 'programa', 'servicio', 'cómo', 'realiza', 'inscripción', 'emprendimiento'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo el programa de emprendimiento?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['costo', 'programa', 'tiene', 'emprendimiento'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La solicitud de inscripción al programa de emprendimiento se puede hacer en línea?', 'Si, se puede realizar a través del correo o wsp.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['solicitud', 'programa', 'inscripción', 'emprendimiento', 'puede'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La solicitud de inscripción al programa de emprendimiento se puede hace presencial?', 'Si, en la cr. 10 No. 8-72, Centro Comercial el Curubito, Chía, Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['solicitud', 'presencial', 'programa', 'inscripción', 'emprendimiento', 'puede'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa de asistencia técnica agropecuaria?', 'Es el proceso a través del cual se da soporte a un actor o a un pequeño grupo de actores sociales, a partir de la experticia de una persona o institución que comparte conocimientos, herramientas, técnicas y puntos de vista, con el fin de lograr un producto o resultado determinado Requisitos para ingresar al programa de asistencia técnica agropecuaria Ser Residente del Municipio de Chía​', 
 (SELECT id FROM faq_themes WHERE name = 'CAPACITACIÓN EMPRESARIAL'), 
 ARRAY['requisito', 'programa', 'asistencia', 'consiste', 'técnica', 'agropecuaria'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa de asistencia técnica agropecuaria?', 'El usuario puede hacer la solicitud de asistencia técnica a través de medio presencial en la oficina de la Dirección de Desarrollo Agropecuario y Empresarial (D.D.A.E.), a través del correo electrónico (<EMAIL>), o WhatsApp (3172977503). El funcionario de la D.D.A.E., diligencia el formulario único de extensión rural de acuerdo a los datos suministrados por el solicitante. La solicitud puede ser agrícola, pecuaria o urgencias veterinarias. Nota 1: las solicitudes de bienestar animal no requieren el formato en mención, deben ser radicadas. Nota 2: De requerir el tractor se diligencia el formato liquidación servicios de maquinaria agrícola con el fin de que el usuario se dirija a la Secretaría de Hacienda para liquidar y ejecutar el pago ante la entidad bancaria.', 
 (SELECT id FROM faq_themes WHERE name = 'CAPACITACIÓN EMPRESARIAL'), 
 ARRAY['solicitud', 'presencial', 'programa', 'servicio', 'asistencia', 'cómo', 'realiza', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo el programa de asistencia técnica agropecuaria?', 'La asistencia técnica no tiene costo pero si se requieren servicios de mecanización si tiene costo de acuerdo al nivel del sisben.', 
 (SELECT id FROM faq_themes WHERE name = 'CAPACITACIÓN EMPRESARIAL'), 
 ARRAY['costo', 'programa', 'servicio', 'asistencia', 'tiene', 'técnica'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La solicitud de inscripción al programa de asistencia técnica agropecuaria se puede hacer en línea?', 'Si, se puede realizar a través del correo o WhatsApp.', 
 (SELECT id FROM faq_themes WHERE name = 'CAPACITACIÓN EMPRESARIAL'), 
 ARRAY['solicitud', 'programa', 'asistencia', 'inscripción', 'técnica'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La solicitud de inscripción al programa de asistencia técnica agropecuaria se puede hacer presencial?', 'Si, en la cr. 10 No. 8-72, Centro Comercial el Curubito, Chía, Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'CAPACITACIÓN EMPRESARIAL'), 
 ARRAY['solicitud', 'presencial', 'programa', 'asistencia', 'inscripción', 'técnica'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿EN QUÉ CONSISTE LA AGENCIA PUBLICA DE EMPLEO (APE)?', 'La Agencia Pública de Gestión y Colocación de Empleo de Chía, bajo la Unidad del Servicio Público de Empleo (SPE), adscrita al Ministerio del Trabajo, tiene como misión administrar y promocionar el Servicio Público de Empleo, garantizando un funcionamiento eficiente y oportuno.', 
 (SELECT id FROM faq_themes WHERE name = 'APOYO AL EMPRENDIMIENTO'), 
 ARRAY['servicio', 'consiste', 'agencia', 'publica', 'empleo', 'agencia'], 1);
