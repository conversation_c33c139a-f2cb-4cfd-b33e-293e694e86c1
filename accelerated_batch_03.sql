-- Accelerated FAQ Completion Batch 3
-- Questions 224 to 303
-- Total questions in batch: 80

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede expedir el recibo de impuesto predial de manera virtual?', 'Si, cada contribuyente puede ingresar, descargar y pagar por PSE por la página de la alcaldía, trámites y servicios – impuesto predial – ingresan con su usuario y contraseña y descargan su factura.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['virtual', 'servicio', 'puede', 'expedir', 'recibo', 'impuesto', 'predial'], 12);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Puedo pedir un descuento de intereses si pago el total adeudado de impuesto predial?', 'Todo descuento, alivió debe ser aprobado mediante Acuerdo Municipal, aprobado por el Consejo, de no ser así, o estar establecido, no se puede hacer descuentos o acceder a las solicitudes de los contribuyentes.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['solicitud', 'puedo', 'pedir', 'descuento', 'intereses', 'pago'], 13);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué hay de cierto que los mayores de 60 años no pagan impuesto predial?', 'Es información incorrecta, no existe regulación normativa al respecto que avale tal situación, todos sin excepción deben pagar sus impuestos.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['cierto', 'mayores', 'años', 'pagan', 'impuesto'], 14);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué personas están exoneradas de pagar impuesto predial?', 'No hay nadie exento del pago de impuesto predial', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['personas', 'están', 'exoneradas', 'pagar', 'impuesto'], 15);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Puedo ceder una parte de mi predio al municipio para cubrir el pago de mi impuesto predial?', 'No, no se puede ceder parte o un predio al municipio para suplir la obligación del impuesto predial', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['puedo', 'ceder', 'parte', 'predio', 'municipio'], 16);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Colombia Mayor?', 'El Programa de Protección Social al Adulto Mayor – “Colombia Mayor” es un programa del orden Nacional que busca aumentar la protección a los adultos mayores por medio de la entrega de un subsidio económico para aquellos que se encuentran desamparados, que no cuentan con una pensión, o viven en la extrema pobreza. Requisitos para ingresar al programa Colombia mayor Dirigirse a la Dirección de Acción social Entregar los siguientes requisitos para verificación por parte de acción social Puntaje de Sisbén A, B hasta C1. No tener casa (esta información se verifica en RUAF). No tener pensión EPS subsidiada, en caso de no serlo se verificar el ingreso base de cotización. Mujeres se puede inscribir desde los 54 años y los hombres desde los 59 años. Tienen mayor prioridad personas mayores de 80 años, víctimas del conflicto armado y personas con discapacidad certificada por la Secretaría de Salud Municipal. La asignación de cupo depende del departamento para la prosperidad social del orden Nacional.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'programa', 'consiste', 'colombia', 'mayor'], 17);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Colombia Mayor?', 'Se realiza la inscripción en la Dirección de Acción social.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'colombia'], 18);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo el programa Colombia Mayor?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'colombia', 'mayor'], 19);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Colombia Mayor se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'colombia', 'mayor', 'puede'], 20);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede realizar la inscripción de forma presencial al programa Colombia Mayor?', 'Si, en la CRA 7 NO. 12-100. TEMA :PROGRAMA RENTA CIUDADANA', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'realizar', 'inscripción', 'forma'], 21);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Renta Ciudadana?', 'Renta Ciudadana Es un programa del DPS del Orden Nacional que busca contribuir a la superación de la pobreza, fomentar la movilidad social y apoyar la economía local, mediante la entrega de Transferencias Monetarias Condicionadas y No Condicionadas, siguiendo los principios de integralidad, efectividad y eficiencia. Su implementación se llevará a cabo de manera gradual y progresiva a través de diversas líneas de intervención. La focalización la realiza directamente el DPS y allega a la Dirección de Acción social una base de datos con las familias que han sido focalizadas. Requisitos para ingresar al programa Renta Ciudadana Población Sisbén e Indígena Sisbén de A a B4, si es población indígena se valida el certificado SIIC que expide el ministerio del Interior. Ser colombiano Esperar comunicado por parte del enlace Municipal Acercarse de manera personal', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'certificado', 'programa', 'consiste', 'renta', 'ciudadana', 'renta'], 22);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Renta Ciudadana Población Sisbén e Indígena?', 'No se realiza inscripción, pues el DPS selecciona a las familias beneficiarias y comunica a la Dirección de Acción Social.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'renta'], 23);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Renta Ciudadana Población Sisbén e Indígena?', 'No.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'renta'], 24);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción la inscripción al programa Renta Ciudadana Población Sisbén e Indígena se puede hacer en línea?', 'No.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'inscripción', 'renta', 'ciudadana'], 25);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer la inscripción al programa Renta Ciudadana Población Sisbén e Indígena de forma presencial?', 'No, el gobierno realiza la priorización desde las bases de datos. TEMA : PROGRAMAHABITANTE DE CALLE', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción', 'renta'], 26);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa habitante de Calle?', 'Es una estrategia que busca reducir los índices de habitabilidad para las personas que hacen de la calle su lugar de residencia permanente o transitoria, o de manera intermitente residen en diversos tipos de vivienda que combina la vivienda inadecuada, insegura y la calle, que hasta el momento se ha señalado bajo la denominación "personas en situación de calle", como la manera más clara de establecer el universo de personas hacia las cuales se deben adelantar acciones de inclusión e integración social debida a su extrema situación de exclusión socio habitacional. Requisitos para ingresar al programa habitante de Calle Dirigirse al centro transitorio de protección para que activen ruta de atención (Verificar procedencia, afiliación a Salud, Sisbén, ubicar red de apoyo, activar triage…)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'programa', 'apoyo', 'consiste', 'habitante', 'calle', 'estrategia'], 27);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa habitante de Calle?', 'No aplica, se realiza en Centro Transitorio de Protección Municipal', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'habitante'], 28);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo ingresar al programa habitante de Calle?', 'No,', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'ingresar', 'habitante'], 29);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa habitante de Calle se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'habitante', 'calle', 'puede'], 30);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer presencial la inscripción al programa habitante de Calle?', 'Si en el centro transitorio de protección de Chía, pues es necesaria la activación de la ruta de atención para las atenciones al habitante de calle. TEMA : PROGRAMARENTA JOVEN', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción'], 31);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Renta Joven?', 'El programa Renta Joven es la evolución de Jóvenes en Acción y surge con el ánimo de contribuir a la inclusión social y económica de las juventudes. Este programa es de Orden Nacional que fomenta el acceso, permanencia y graduación de la educación superior y formación complementaria para acompañar el proceso durante un ciclo educativo: técnico, tecnólogo o pregrado, mediante la entrega de transferencias monetarias condicionadas. Renta Joven comienza a operar en 2024. Requisitos para ingresar al programa Renta Joven Realizar pre-registro en las fechas establecidas en la plataforma de prosperidad social en la plataforma de renta joven. Esperar comunicado del DPS en el correo electrónico registrado en el pre-registro.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'registro', 'programa', 'consiste', 'renta', 'joven'], 32);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Renta Joven?', 'Es necesario realizar el Pre-registro: Jóvenes en acción (prosperidadsocial.gov.co)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['registro', 'programa', 'cómo', 'realiza', 'inscripción', 'renta'], 33);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa Renta Joven?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'renta'], 34);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Renta Joven se puede hacer en línea?', 'Si, Jovenes en acción (prosperidadsocial.gov.co)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'renta', 'joven', 'puede'], 35);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hacer presencial la inscripción al programa Renta Joven?', 'No TEMA : PROGRAMA ATENCIÓN INTEGRAL PARA ADULTO MAYOR', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'puede', 'hacer', 'inscripción'], 36);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Atención Integral Para el Adulto Mayor?', 'Este programa busca ocupar el tiempo libre de los adultos mayores, en el que se capacitan con diferentes talleres y actividades diarias, adicional realizan actividades físicas y juegos autóctonos. El programa cuenta con 13 puntos de atención en diferentes sectores y veredas del Municipio y se atienden dos grupos más en casa día Fagua, durante las actividades diarias se brinda a todos los adultos mayores un refrigerio y almuerzo, otra de las modalidades es la atención domiciliaria en la que se realiza entrega de un paquete alimentario mensual y la última modalidad es la atención prioritaria que es atendida en casa día Fagua en la que se brindan talleres, actividades y desayuno, refrigerio y almuerzo. Requisitos para ingresar al programa Atención Integral Para el Adulto Mayor Sisbén del Municipio de Chía Fotocopia de la cedula de ciudadanía Fotocopia de un servicio público (Luz o Agua)', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'programa', 'servicio', 'consiste', 'atención', 'integral', 'para'], 37);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Atención Integral Para el Adulto Mayor?', 'Se debe acercar a la Dirección de Acción Social en la Cra. 7 # 12-100, para inscribirse en la base de datos de espera.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'atención'], 38);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa Atención Integral Para el Adulto Mayor?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'atención'], 39);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Atención Integral Para el Adulto Mayor se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'atención', 'integral', 'para'], 40);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Atención Integral Para el Adulto Mayor Se puede hacer presencial?', 'Si, en la CRA 7 NO. 12-100. TEMA : PROGRAMA PRIMERA INFANCIA-JARDINES SOCIALES', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'inscripción', 'atención', 'integral', 'para'], 41);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Primera Infancia-Jardines Sociales?', 'El programa social de la primera infancia está orientado en la atención de niñas y niños del Municipio Chía que se encuentran entre las edades de 0 a 5 años. Este busca la atención integral de los niños, niñas beneficiadas de nuestros jardines sociales, logrando así atender a la población más vulnerable del Municipio. El programa cuenta con una cobertura de 580 cupos para que nuestros niños, niñas desarrollen habilidades sociales, emocionales y cognitivas a través de la atención integral, la cual está dirigida por profesionales en el área de educación primaria y equipo psicosocial. Requisitos para ingresar al programa Primera Infancia-Jardines Sociales Fotocopia del registro civil Fotocopia del carnet de vacunas Certificación de afiliación a EPS Certificado médico de: Audiometría, Optometría y Odontología Certificado de crecimiento y desarrollo Certificado del Sisbén 5 fotos 3*4 Fotocopia del último recibo de energía o agua de donde reside Fotocopia de documento del acudiente', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'documento', 'certificado', 'registro', 'programa', 'consiste', 'primera', 'infancia'], 42);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Primera Infancia-Jardines Sociales?', 'La inscripción se realiza con el registro civil del menor para verificar la cobertura que se cuenta en los jardines ubicados en las tres veredas del Municipio. Sus datos quedan registrados en la base de datos del programa de la Dirección de Acción Social en la Cra. 7 # 12-100, allí se envía los datos a las coordinadoras de los Jardines Sociales y se asigna el cupo dependiendo del Sector o Vereda de donde viva la familia.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['registro', 'programa', 'cómo', 'realiza', 'inscripción', 'primera'], 43);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo ingresar al programa Primera Infancia-Jardines Sociales?', 'El programa es totalmente gratuito para las familias del Municipio de Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'gratuito', 'programa', 'tiene', 'ingresar', 'primera'], 44);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Primera Infancia-Jardines Sociales se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'primera', 'infancia', 'jardines'], 45);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Primera Infancia-Jardines Sociales Se puede hace presencial?', 'Si, en la CRA 7 NO. 12-100. TEMA :PROGRAMA DISCAPACIDAD', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'inscripción', 'primera', 'infancia', 'jardines'], 46);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa de Discapacidad?', 'El programa cuenta con 6 modalidades, la modalidad transitoria buscar realizar una inclusión educativa en las Instituciones Educativas del Municipio, en el que se realiza valoración inicial y capacitación de inclusión a educadores, alumnos y padres de familia, la modalidad de atención en cetro día busca ocupar el tiempo libre por medio de actividades y talleres diarios, en esta modalidad e brinda refrigerio y almuerzo, el servicio es prestado de lunes a viernes de 8:00am a 3:00pm, la modalidad domiciliaria entrega un paquete alimentario mensual, la modalidad de generación de ingresos propios busca capacitar en diferentes artes y técnicas para que las personas con discapacidad a partir de estos talleres coloquen sus unidades productivas y generen ingresos, la modalidad inclusión laboral capacita en diferentes áreas para ser incluidos en empresas o que puedan crear su unidades productivas, la modalidad apoyo terapéutico brinda terapia en diferentes áreas a niños de 2 meses a 12 años con la fundación FEL. Requisitos para ingresar al programa de Discapacidad Fotocopia del Certificado de Discapacidad Fotocopia Documento de Identificación Fotocopia de un servicio Público (Agua- Luz) Sisbén del Municipio de Chía', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'documento', 'certificado', 'programa', 'servicio', 'apoyo', 'consiste', 'discapacidad'], 47);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa de Discapacidad?', 'Se debe acercar a la Dirección de Acción Social en la Cra. 7 # 12-100, para inscribirse en la base de datos de espera.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción', 'discapacidad'], 48);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa de Discapacidad?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'discapacidad'], 49);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa de Discapacidad se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'inscripción', 'discapacidad', 'puede', 'hacer'], 50);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa de Discapacidad Se puede hace presencial?', 'Si, en la CRA 7 NO. 12-100.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'inscripción', 'discapacidad', 'puede', 'hace'], 51);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Gobierno Municipal Estudiantil?', 'Un mecanismo de participación democrática donde se les da la posibilidad a los estudiantes de las diferentes instituciones públicas y privadas de chía, de los niveles de Educación secundaria, media y técnica académica y/o técnica de autogobernar, auto gestionar su propia necesidad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Gobierno Municipal Estudiantil?', 'Link: http://inscripciongobiernoestudiantil.vuvchia.com/', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['inscripcion', 'programa', 'cómo', 'realiza', 'inscripción', 'gobierno'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo inscribirse al programa Gobierno Municipal Estudiantil?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'tiene', 'inscribirse', 'gobierno'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Gobierno Municipal Estudiantil se puede hacer en línea?', 'Si, link: http://inscripciongobiernoestudiantil.vuvchia.com/', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['inscripcion', 'programa', 'inscripción', 'gobierno', 'municipal', 'estudiantil'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Gobierno Municipal Estudiantil se puede hace presencial?', 'La inscripción No, pero la votación sí, en los lugares asignados.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'inscripción', 'gobierno', 'municipal', 'estudiantil'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Plataforma De Juventudes?', 'Son escenarios de encuentro, articulación, coordinación e interlocución de las juventudes, de carácter autónomo. Por cada ente territorial deberá existir una plataforma. La Plataforma Local, Municipal y Distrital de Juventudes será conformada por un número plural de procesos y prácticas organizativas, así como por espacios de participación de los LEY ESTATUTARIA 1622 DE 2013 MODIFICADA POR LA LEY ESTATUTARIA 1885 DE 2018 71 y las jóvenes. Requisitos para ingresar al programa Plataforma De Juventudes Esperar la apertura de la convocatoria para la actualización de la plataforma de juventudes por parte del municipio. Copia de documento de identidad los integrantes del colectivo, proceso o practica organizativa. Diligenciamiento de formulario Carta de intención por parte del colectivo Esperar expedición del acto administrativo por parte de la personería Tener edades entre 14 a 28 años', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['requisito', 'documento', 'programa', 'consiste', 'plataforma', 'juventudes', 'escenarios'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Plataforma De Juventudes?', 'En la Dirección de Ciudadanía Juvenil, de manera presencial', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'plataforma'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Plataforma De Juventudes?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'plataforma'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Plataforma De Juventudes se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'inscripción', 'plataforma', 'juventudes', 'puede'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Plataforma De Juventudes se puede hace presencial?', 'Si, En la Dirección de Ciudadanía Juvenil', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'inscripción', 'plataforma', 'juventudes', 'puede'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Concejo Municipal De Juventudes?', 'Los Consejos de Juventudes son mecanismos autónomos de participación, concertación, vigilancia y control de la gestión pública e interlocución de los y las jóvenes en relación con las agendas territoriales de las LEY ESTATUTARIA 1622 DE 2013 MODIFICADA POR LA LEY ESTATUTARIA 1885 DE 2018 43 juventudes, ante institucionalidad pública de cada ente territorial al que pertenezcan, y desde las cuales deberán canalizarse los acuerdos de los y las jóvenes sobre las alternativas de solución a las necesidades y problemáticas de sus contextos y la visibilización de sus potencialidades y propuestas para su desarrollo social, político y cultural ante los gobiernos territoriales y nacional. Nota: está en curso modificación de la normatividad por parte del congreso. Requisitos para ingresar al programa al Programa Concejo Municipal De Juventudes Estar en el rango de edad establecido en la presente ley. Los jóvenes entre 14 y 17 años deberán presentar copia del registro civil de nacimiento o tarjeta de identidad. Así mismo los jóvenes entre 18 y 28 años deberán presentar la cédula de ciudadanía o contraseña. Tener domicilio o demostrar que realiza una actividad laboral, educativa o de trabajo comunitario, en el territorio al cual aspira representar, mediante declaración juramentada ante una Notaría. Estar inscrito en una lista presentada por los jóvenes independientes, o por un movimiento o partido político con personería jurídica. En el caso de los procesos y prácticas organizativas juveniles ser postulado por una de ellas. Presentar ante la respectiva Registraduría, una propuesta de trabajo que indique los lineamientos a seguir como consejero de juventud, durante su periodo.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['requisito', 'registro', 'linea', 'programa', 'consiste', 'concejo', 'municipal', 'juventudes'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa al Programa Concejo Municipal De Juventudes?', 'En los sitios o links asignados por la registraduría Municipal.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'cómo', 'realiza', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la postulación al programa al Programa Concejo Municipal De Juventudes?', 'La postulación No, pero debe anexar la declaración juramentada que reside o trabaja en el Municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'tiene', 'postulación'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa al Programa Concejo Municipal De Juventudes se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'inscripción', 'concejo', 'municipal'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Concejo Municipal De Juventudes Se puede hacer presencial?', 'Si, en la registraría Municipal de Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'inscripción', 'concejo', 'municipal', 'juventudes'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'Consiste en un programa de la red de bibliotecas, que representa una experiencia que permite desarrollar el pensamiento, el lenguaje, la imaginación, la creatividad;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'La inscripción se realiza de forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'promoción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Promoción De Lectura, Escritura Y Oralidad – LEO?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'promoción'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Promoción De Lectura, Escritura Y Oralidad – LEO se puede hacer en línea?', 'forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'inscripción', 'promoción', 'lectura', 'escritura'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Promoción De Lectura, Escritura Y Oralidad – LEO Se puede hace presencial?', 'Si, en las instalaciones de la Biblioteca Municipal o en todas las sedes.', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'inscripción', 'promoción', 'lectura', 'escritura'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el Programa Forma TIC de la Red de Bibliotecas?', 'Consiste en un servicio de carácter pedagógico que estimula el uso adecuado de las Bibliotecas y fomenta las competencias básicas en el acceso y uso de la información y la tecnología. Para lo cual se ofertan los siguientes cursos: 1. Reconoce tu biblioteca 2. Aprende a explorar fuentes de información 3.Tutorias en información básica 4. Información y TIC en finanzas personales 5. Técnicas para el aprendizaje efectivo 6. Herramientas para el aprendizaje autónomo del inglés 7. Iniciación a la creación literaria 8. Seguridad en información y Tic 9. Prácticas de lectura, Escritura y Oralidad-LEO 10.Sensibilizacion Ambiental Requisitos para ingresar al Programa Forma TIC de la Red de Bibliotecas Revisar oferta de cursos Consultar en las bibliotecas o realizar requerimiento a la biblioteca', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['requisito', 'programa', 'servicio', 'consiste', 'forma', 'bibliotecas', 'consiste'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al Programa Forma TIC de la Red de Bibliotecas?', 'Se realiza de forma presencial en la Carrera 7 No. 15-51 Chía – Cundinamarca – Colombia y en todas las sedes. Correo: <EMAIL> Teléfono: 8844444 Ext 3605', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'forma'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al Programa Forma TIC de la Red de Bibliotecas?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'forma'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al Programa Forma TIC de la Red de Bibliotecas se puede hacer en línea?', 'Si, <EMAIL> Servicios de Red de bibliotecas SERVICIOS | Red de Bibliotecas Públicas del Municipio de Chía – RBPMC (wordpress.com) SERVICIOS – Red de Bibliotecas Públicas Municipio de Chía (chia-cundinamarca.gov.co)', 
 (SELECT id FROM faq_themes WHERE name = 'BIBLIOTECA HOQABIGA'), 
 ARRAY['programa', 'servicio', 'inscripción', 'forma', 'bibliotecas', 'puede'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al Programa Forma TIC de la Red de Bibliotecas se puede hacer presencial?', 'Si, En la Biblioteca HOQABIGA Carrera 7 No. 15-51 Chía;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa servicio social para estudiantes de grado 10 y 11, en las bibliotecas públicas del municipio de chía?', 'El Servicio social obligatorio que deben realizar los estudiantes en los grados 10 u 11, se puede realizar en La Red de Bibliotecas Públicas del municipio. Fundamentada en la Resolución 4210 de 1996, es para que los estudiantes de las instituciones educativas oficiales y privadas del municipio que estén cursando 10° y 11° realicen su Servicio Social Estudiantil Obligatorio SSEO;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía?', 'La inscripción se realiza de manera presencial en las instalaciones a la dirección de cultura', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'servicio', 'cómo', 'realiza', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['costo', 'programa', 'servicio', 'tiene', 'inscripción'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea la inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['programa', 'servicio', 'inscripción', 'puede', 'hacer', 'línea', 'inscripción'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa de servicio social obligatorio en las bibliotecas públicas del municipio de Chía Se puede hace presencial?', 'Si, Carrera 7 No. 15-51 Chía – Chía, Cundinamarca – Colombia', 
 (SELECT id FROM faq_themes WHERE name = 'CASA DE LA JUVENTUD'), 
 ARRAY['presencial', 'programa', 'servicio', 'inscripción', 'social', 'obligatorio'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste el programa escuela De Formación Artística De Chía?', 'Consiste en un programa de formación académica a la institución de educación para el trabajo y el desarrollo humano denominado ESCUELA DE FORMACION ARTISTICA Y CULTURAL DE CHIA que tiene cubrimiento en todo el municipio (juntas de acción comunal, conjuntos residenciales, fundaciones, CDI e Instituciones Educativas Oficiales);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa escuela De Formación Artística De Chía?', 'La inscripción se realiza de forma presencial en las instalaciones de la Biblioteca 2 piso con el funcionario Iván Manrique.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['presencial', 'programa', 'cómo', 'realiza', 'inscripción', 'escuela'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa escuela De Formación Artística De Chía?', 'Si, depende de la Clasificación del Sisbén para los puntajes de C15-C16 pagan $34.000 y C17 A D20 pagan $68.000, D21 o los que no tienen Sisbén pagan $171.000 para el año 2024.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['costo', 'programa', 'tiene', 'inscripción', 'escuela'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa escuela De Formación Artística De Chía se puede hacer en línea?', 'No, pero próximamente se habilitará link.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['programa', 'inscripción', 'escuela', 'formación', 'artística'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa escuela De Formación Artística De Chía Se puede hace presencial?', 'Si, Carrera 7 No. 15-51 Chía Segundo piso– chía, Cundinamarca – Colombia.', 
 (SELECT id FROM faq_themes WHERE name = 'ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL'), 
 ARRAY['presencial', 'programa', 'inscripción', 'escuela', 'formación', 'artística'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste Beneficios Económicos Periódicos para artistas y gestores culturales?', 'Es un programa del MINISTERIO DE CULTURA donde se busca ayudar y beneficiar a artistas y gestores culturales que por su oficio nunca lograron acceder a una pensión. La alcaldía de Chía en cabeza de la dirección de cultura coordina la convocatoria de recepción de documentos de las personas interesadas en acceder a este beneficio, así mismo es la, entidad encargada de girar el recurso, este proviene del recaudo correspondiente al 10% de la estampilla pro-cultura. Una vez recopilada la lista de postulados se remiten al ministerio de cultura y este es quien hace la lista de priorización de beneficiaros. El valor del aporte entregado a los artistas o gestores culturales beneficiados corresponde al 30% de cada mes del salario mínimo y llega cada dos meses. Requisitos para acceder al programa Beneficios Económicos Periódicos para artistas y gestores culturales 1. Ser colombiano. 2. Tener mínimo 62 años de edad si es hombre y 57 años de edad si es mujer. 3. Residir durante los últimos diez (10) años en el territorio nacional. 4. Percibir ingresos inferiores a un (1) Salario Mínimo Mensual Legal Vigente (SMMLV). 5. Que se acredite, a través del Ministerio de Cultura, la condición de gestor o creador cultural, de acuerdo con los requisitos que esa cartera ministerial determine para tal fin. 6. no tener pensión 7. certificado de residencia expedido por la oficina asesora jurídica que acredite su residencia en el municipio de chía. 8. en el sistema de salud debe figurar como beneficiario o subsidiado (si aparece como cotizante, no queda beneficiado).', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['requisito', 'documento', 'certificado', 'programa', 'beneficio', 'consiste', 'beneficios', 'económicos'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales?', 'La recepción de documentos está abierta durante todo el tiempo acercándose de manera presencial a la Dirección de cultura. La Dirección de cultura informara al interesado la lista de documentos que debe reunir y le entregara el formato establecido por el ministerio de cultura.', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['documento', 'presencial', 'programa', 'beneficio', 'cómo', 'realiza', 'inscripción', 'beneficios'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo la inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['costo', 'programa', 'beneficio', 'tiene', 'inscripción', 'beneficios'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['programa', 'beneficio', 'inscripción', 'beneficios', 'económicos', 'periódicos'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción al programa Beneficios Económicos Periódicos para artistas y gestores culturales Se puede hace presencial?', 'Si, Carrera 7 No. 15-51 Chía Segundo piso– chía, Cundinamarca – Colombia. Más información en: https://sifo.mincultura.gov.co/home', 
 (SELECT id FROM faq_themes WHERE name = 'ADULTO MAYOR'), 
 ARRAY['presencial', 'programa', 'beneficio', 'inscripción', 'beneficios', 'económicos', 'periódicos'], 5);
