-- Accelerated FAQ Completion Batch 1
-- Questions 64 to 143
-- Total questions in batch: 80

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quiénes son los nuevos uniformados que están en el municipio?', 'El Alcalde Leonardo Donoso Ruiz en un trabajo constante por velar por la seguridad del Municipio de Chía y sus habitantes, gestionó con el Ministerio de Defensa Nacional, la llegada del grupo GOES quienes estarán acantonados en el municipio, en el CAI perimetral y estarán realizando operatividad constante, distribuidos con de acuerdo a las necesidades en seguridad de cada sector del Municipio de Chía', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quiénes', 'nuevos', 'uniformados', 'están', 'municipio'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En que se enfocará el GOES?', 'El GOES estará enfocado en labores referentes al orden público, delitos que tengan relación con lesiones personales, extorción, hurto, porte ilegal de estupefacientes, secuestro. Intimidación o engaño, abuso de menores, turismo sexual y contrabando, iintervenir hasta la llegada del Grupo Especial de Operaciones en situaciones de alto riesgo como incidentes con rehenes, entre otras.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['enfocará', 'goes', 'goes', 'estará', 'enfocado'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Familiares reportan que uno de sus miembros no ha regresado a la casa después de unos días, están preocupados por su ausencia, consultan en dónde podría estar?', 'Puede estar en el Centro de detención transitorio – (CDT) o puede estar en el Centro Transitorio de Protección (CTP) Cuál es la razón de encontrarse en el Centro de detención transitorio – (CDT) Puede estar detenido, por haber cometido presuntamente un delito.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['familiares', 'reportan', 'miembros', 'regresado', 'casa'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las razones por las cuales una persona puede encontrarse en el Centro Transitorio de Protección (CTP)?', 'Según lo estipulado en el Artículo 155 de la Ley 1801 de 2016, Código de Seguridad y Convivencia Ciudadana. Cuando la vida e integridad de una persona natural se encuentre en riesgo o peligro y no acepte la mediación policial corno mecanismo para la solución del desacuerdo, el personal uniformado de la Policía Nacional, podrá trasladarla para su protección en los siguientes casos: A. Cuando se encuentre inmerso en riña. B. Se encuentre deambulando en estado de indefensión. C. Padezca alteración del estado de conciencia por aspectos de orden mental. D. Se encuentre o aparente estar bajo efectos del consumo de bebidas alcohólicas o sustancias psicoactivas ilícitas o prohibidas y exteriorice comportamientos agresivos o temerarios E. Realice actividades peligrosas o de riesgo que pongan en peligro su vida o integridad, o la de terceros. h) Se encuentre en peligro de ser agredido.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'razones', 'cuales', 'persona', 'puede'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el trámite a seguir estando en el Centro de detención transitorio – (CDT)?', 'El policía que lo detuvo, está obligado a poner el informe a disposición del Fiscal de turno, para que este solicite al Centro de Servicios Judiciales la asignación de un juez de control de garantías, y lo llevaran para realización de las 3 audiencias – que son;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el trámite a seguir estando en el Centro Transitorio de Protección (CTP)?', 'Cumplidas 12 horas, a partir de que la policía lo llevó para protegerlo, debe permitírsele su salida, pero si la persona se comunica con un familiar o con su representante y este se acerca al CTP a fin de recogerlo, la Policía o miembro del Centro debe entregarlo. Cuáles Derechos se le deben garantizar a quienes se encuentren en el Centro de detención transitorio – (CDT) Tiene derecho a Guardar silencio, no auto incriminarse, comunicarse con un familiar, o con quien considere, tiene derecho a que se le nombre un defensor público y recibir buen trato.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'trámite', 'seguir', 'estando', 'centro'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles Derechos se le deben garantizar a quienes se encuentren en el Centro Transitorio de Protección (CTP)?', 'Tiene Derecho a recibir atención del grupo psicosocial, CTP (abogado, psicólogo y auxiliar de enfermería) Tiene Derecho a que el delegado del Ministerio Público (Personero en CTP) revise si la protección es legal o no, si no es legal el mismo personero debe ordenar al grupo del CTP que le permita su salida – ejm – debe estar incurso en una de las causales del art 155 y de la sentencia C310 DE 2023 Corte Constitucional.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'derechos', 'deben', 'garantizar', 'quienes'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo puedo verificar si se encuentra en el Centro de detención transitorio – (CDT) o en el Centro Transitorio de Protección (CTP)?', 'a. Consultando al observatorio de Seguridad y convivencia ciudadana (hora laboral) Carrera 7 – No 12 - 100 b. En hora no laboral se debe dirigir a la variante con segunda, calle 2 No 6 – 35 Avenida Variante, al lado de la sala de velación. – Edf azul. c. Diríjase al comando de policía del Curubito Calle 9 N 10- 63, cerca al parque central del Chía, ahí podrá solicitar información.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'puedo', 'verificar', 'encuentra', 'centro'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el tiempo de permanencia en el Centro de detención transitorio – (CDT)?', 'Máximo 36 horas contadas desde la captura hasta que termina la audiencia de legalización de la captura, luego de lo cual el policía debe alistar la carpeta para conducir al detenido al INPEC de la cárcel de mediana seguridad de Chocontá y/o a la Cárcel Modelo con esta última próximamente se formalizará convenio para tal fin.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'tiempo', 'permanencia', 'centro', 'detención'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué documentos debe entregar el Centro de detención transitorio – (CDT) al INPEC?', 'La carpeta de documentos debe contener: informe de captura lo hace le policía que lo retiene – acta de derechos de buen trato- tarjeta decadactilar- orden de medida de aseguramiento expedida por el juez- acta de la audiencia.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['documento', 'documentos', 'debe', 'entregar', 'centro', 'detención'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el tiempo de permanencia en el Centro Transitorio de Protección (CTP)?', 'Max 12 horas, si la auxiliar de enfermería considera que no está en sus capacidades, el grupo interdisciplinario lo debe remitir por intermedio de la policía al hospital o clínica más cercana.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'tiempo', 'permanencia', 'centro', 'transitorio'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué documentos debe contener la carpeta de la persona que ha estado en protección en el Centro transitorio de Protección (CTP)?', 'Formato de enfermería, Psicología, policía, abogado y personero en CTP.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['documento', 'documentos', 'debe', 'contener', 'carpeta', 'persona'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quién administra el Centro de detención transitorio – (CDT)?', 'El Director de Seguridad y Convivencia Ciudadana con el comandante de estación del Curubito', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quién', 'administra', 'centro', 'detención', 'transitorio'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quién administra el Centro transitorio de Protección (CTP)?', 'El Director de Seguridad y Convivencia Ciudadana con el líder profesional de planta de la Alcaldía Municipal CTP.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quién', 'administra', 'centro', 'transitorio', 'protección'], 12);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué norma se aplica en el Centro de detención transitorio – (CDT)?', 'Código Penal y de Procedimiento Penal Ley 906 de 2004 y Ley 600 de 2000', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['procedimiento', 'norma', 'aplica', 'centro', 'detención', 'transitorio'], 13);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué norma se aplica en el Centro transitorio de Protección (CTP)?', 'Código Nacional de Seguridad y Convivencia Ciudadana. Ley 1801 de 2016', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['norma', 'aplica', 'centro', 'transitorio', 'protección'], 14);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es El PISCC?', 'Es el instrumento de planeación estratégica para la gestión de la convivencia y la seguridad ciudadana territorial, en todos los departamentos, distritos y municipios del país, debe ser formulado y aprobado en los primeros seis meses de mandato de las nuevas administraciones locales (Artículos 201 y 205 Ley 1801 de 2016 – Modificado por el artículo 41 de la Ley 2197 de 2022). Contiene estrategias y líneas de acción que se deben convertir en programas y proyectos que tengan como finalidad dar solución, reducir o prevenir problemas priorizados de criminalidad, delincuencia, comportamientos contrarios a la convivencia, acceso a la justicia y factores de riesgo que afectan a las personas.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['programa', 'piscc', 'instrumento', 'planeación', 'estratégica', 'para'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la convivencia?', 'El Código Nacional de Seguridad y Convivencia Ciudadana – CNSCC, en su artículo 5, define la convivencia como la interacción pacífica, respetuosa y armónica entre las personas, con los bienes, y con el ambiente, en el marco del ordenamiento jurídico. Las categorías de convivencia son: seguridad, tranquilidad, ambiente y salud pública.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['convivencia', 'código', 'nacional', 'seguridad', 'convivencia'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la Seguridad?', 'El término seguridad se debe asumir como un concepto que ha evolucionado en el tiempo;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En dónde se encuentra ubicado El municipio de Chía?', 'Se encuentra ubicado en la provincia sabana centro del departamento de Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['dónde', 'encuentra', 'ubicado', 'municipio', 'chía'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuándo se conformó la provincia sabana?', 'La provincia sabana se creó mediante Ordenanza No. 023 de 1998 y actualizada con la Ordenanza No. 07 de 2001. inicialmente, dicha Provincia estuvo conformada por nueve (9) municipios: Cajicá, Chía, Cogua, Gachancipá, Nemocón, Sopo, Tabio, Tocancipá y Zipaquirá;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los límites del municipio de Chía?', 'El municipio de Chía limita al norte con el municipio de Cajicá;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuántas veredas está dividida administrativamente Chía?', 'En 9 Veredas más el Área Urbana.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuántas', 'veredas', 'está', 'dividida', 'administrativamente'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las Veredas que conforman el Municipio de Chía?', 'Fagua, Tiquiza, Fonquetá y Cerca de Piedra (Dentro de las veredas de Cerca de Piedra y Fonquetá se encuentra ubicado el Resguardo indígena Mhuysqa.), Bojacá, La Balsa, Samaria, Yerbabuena baja, Fusca y torca', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'veredas', 'conforman', 'municipio', 'chía'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se agrupan las veredas en Chía?', 'Este conjunto de veredas se agrupa en tres zonas: Zona occidental: Fagua, Tiquiza, Fonquetá y Cerca de Piedra. (ubicadas parcialmente sobre los cerros de la Valvanera). Zona central: Bojacá, La Balsa y Samaria. Zona oriental: Yerbabuena baja, Fusca y torca. (Ubicados parcialmente en la vereda del cerro yerbabuena alta). Dentro de las veredas de Cerca de Piedra y Fonquetá se encuentra ubicado el Resguardo indígena Mhuysqa.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'agrupan', 'veredas', 'chía', 'este'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la población de Chía 2024?', 'Hombres 79.147 (48.5 %) Mujeres 84.159 (51.5 %) guridad y Convivencia Ciudadana', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'población', 'chía', 'hombres', 'mujeres'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuánta es la Población rural en Chía 2024? Hombres', 'Rural 24.545 (15 %)', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuánta', 'población', 'rural', 'chía', 'hombres'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuánta es la Población Urbana en Chía 2024?', 'Urbana 138.761 (85 %) Cuanta es la Población Étnica en el Municipio de Chía 3524 indígenas 1056 Grupo étnico afrodescendientes y otras poblaciones étnicas Cuál es la Población migrante en el municipio de Chía Según la información suministrada por parte de la unidad de asuntos migratorios y estadísticos (Migración Colombia), con sede en chía Cundinamarca, entre el año 2021 - 2023 un total de 8.934 ciudadanos migrantes solicitaron permiso de permanencia con fines de radicarse dentro del municipio, estas cifras representan el 5,47% del total de la población;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la población en condición de discapacidad?', 'Solicitantes Certificados de discapacidad 1.758 Con certificado de discapacidad 1.324', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['certificado', 'cuál', 'población', 'condición', 'discapacidad', 'solicitantes'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los delitos priorizados de atención en el municipio de Chía?', 'Homicidios, delitos sexuales, extorsión, consumo de sustancias psicoativas, hurto en todas sus modalidades, violencia intrafamiliar y riñas', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'delitos', 'priorizados', 'atención', 'municipio'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la red cívica?', 'SERVICIO DE VIGILANCIA (DEFENSA CONTROLADA) Fortalecen el cuidado en parques, centro de prácticas deportivas e instituciones educativas según programación. VEHÍCULOS DE RESPUESTAS Se divide el municipio en 2 zonas, 2 vehículos, cada uno apoya 4 cuadrantes. VIGILANCIA SECTORIZADA Se divide el municipio en 8 cuadrantes, Patrullaran 16 motorizados por turno de 8 horas, 24 horas al día, se incrementa el recurso humano para mayor cobertura.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['programa', 'servicio', 'cívica', 'vigilancia', 'defensa', 'controlada'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los sectores en los que se realiza la vigilancia motorizada en Chía?', 'Operación: 16 motos, 3 turnos de 8 horas 2 Motorizados Cerca de Piedra y Fonquetá incluido los cerros 2 Motorizados Tíquiza y Fagua incluido los cerros 2 Motorizados Mercedes de Calahorra 2 Motorizados Bojacá 2 Motorizados Zona Centro 2 Motorizados La Balsa 2 Motorizados Samaria 2 Motorizados Fusca, Yerbabuena, corredor vial Centro Chía - Limite Fontanar y Sector Colombia - MC Donalds', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'sectores', 'realiza', 'vigilancia', 'motorizada'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los perímetros de apoyo de vehículos de la red Cívica?', 'Se divide en dos zonas, así: Zona 1: Vereda Fagua, Vereda Tiquiza, Vereda Fonqueta, Vereda Cerca de Piedra, Perimetro urbano principal Zona 2: Vereda Bojacá, Mercedes de Calahorra, Vereda Yerbabuena, Vereda Fusca, Vereda la Balsa, Vereda Samaria', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['apoyo', 'cuáles', 'perímetros', 'vehículos', 'cívica'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la patrulla Purpura?', 'Es una estrategia que busca proteger a las mujeres de Chía de actos de violencia de género y violencia intrafamiliar, garantizando sus derechos.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['patrulla', 'purpura', 'estrategia', 'busca', 'proteger'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la misión de la patrulla Purpura?', 'Su misión proteger y mitigar cualquier acto de violencia en contra de las mujeres.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'misión', 'patrulla', 'purpura', 'misión'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué clase de asistencia brinda la patrulla Purpura?', 'Según la ley 294 de 1996 ART 20 establece que esta es la asistencia que se debe otorgar: -Conducir a la víctima al centro asistencial más cercano, aunque las lesiones no fueren visibles. - Acompañar a la víctima a sacar sus pertenencias personales posteriormente hasta un lugar seguro. -Asesorar a la víctima sobre como preservar pruebas de los actos de violencia - Orientarla sobre sus derechos y rutas de atención', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['asistencia', 'clase', 'brinda', 'patrulla', 'purpura'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las Rutas de atención de la patrulla purpura?', 'Línea de emergencias 155 Central de emergencias de Chía 123', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'rutas', 'atención', 'patrulla', 'purpura'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es el Centro de traslado por protección (CTP)?', 'Lugar encargado de proteger al individuo como a la comunidad de las situaciones presentadas por la violación al Código Nacional de Policía, con el traslado del infractor a dicho centro, para propender por el mejoramiento de la convivencia en el Municipio de Chía, garantizar la vida e integridad de la persona exaltada y la comunidad en general.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['centro', 'traslado', 'protección', 'lugar', 'encargado'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la misión del Centro de traslado por protección?', 'Su misión es proteger a la comunidad, cuando la vida o integridad se encuentre en peligro.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'misión', 'centro', 'traslado', 'protección'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Con que profesionales cuenta el Centro de traslado por protección en la actualidad?', 'Un Profesional Universitario Supervisor Tres Psicólogas Tres Enfermeras Tres Abogados', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['profesionales', 'cuenta', 'centro', 'traslado', 'protección'], 10);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las Condiciones para aplicar el traslado y protección a la comunidad en el Centro de traslado por protección?', 'Si te encuentras inmerso en riña Si estas deambulando en estado de indefensión Si padeces estado de alteración de conciencia Si te encuentras o aparentas estar bajo efectos de embriaguez o sustancias psicoactivas y exteriorizas. Comportamientos agresivos o temerarios. Realizar actividades que ponen en riesgo tu vida, tu integridad o la de terceros.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'condiciones', 'para', 'aplicar', 'traslado'], 11);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es el Grupo Operativo Especial de Seguridad (GOES)?', 'Es una unidad de élite del Cuerpo Nacional de Policía.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['grupo', 'operativo', 'especial', 'seguridad', 'goes'], 12);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la misión del Grupo Operativo Especial de Seguridad (GOES)?', '-Reducir o neutralizar terroristas o delincuentes peligrosos. -Intervenir hasta la llegada del Grupo Especial de Operaciones en situaciones de alto riesgo como incidentes con rehenes. - Ejecutar protecciones especiales de bienes y personas.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'misión', 'grupo', 'operativo', 'especial'], 13);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué significan las siglas, CIEPS?', 'Centro de Información Estratégico Policial Seccional Qué es el Centro de Información Estratégico Policial Seccional Es un espacio estratégico del orden operacional de la Estación de Policía en el cual se desarrolla el análisis de la información para la planeación, orientación, evaluación y retroalimentación del servicio de policía, son el cerebro de la Estación de Policía.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['servicio', 'significan', 'siglas', 'cieps', 'centro', 'información'], 14);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la SALA CIEPS?', 'Es una sala de crisis para atención de situaciones de extrema complejidad del municipio', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['sala', 'cieps', 'sala', 'crisis', 'para'], 15);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué se va a hacer en la SALA CIEPS?', 'Monitoreo permanente, Análisis de riesgos, Consejos de seguridad y determinaciones de especial trascendencia de seguridad en el municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['hacer', 'sala', 'cieps', 'monitoreo', 'permanente'], 16);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quiénes la integran la SALA CIEPS?', 'El consejo de seguridad y los integrantes del comité de orden público según la necesidad.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quiénes', 'integran', 'sala', 'cieps', 'consejo'], 17);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué significan las siglas CEAS?', 'Centro Administrativo Operativo de Seguridad', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['significan', 'siglas', 'ceas', 'centro', 'administrativo'], 18);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué será el Centro Administrativo Operativo de Seguridad?', 'Será una estructura para el mantenimiento del Orden Público busca transformar la gestión local territorial en materia de seguridad y consolidarse como un centro facilitador de acciones. Articulará las diferentes autoridades administrativas, de investigación y judicialización, respetando sus competencias, unificando acciones, estrategias y mecanismos, para potenciar la respuesta rápida contra el delito.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['será', 'centro', 'administrativo', 'operativo', 'seguridad'], 19);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quiénes harán parte del CEAS?', 'CTI FISCALÍA SIJIN MIGRACIÓN SIPOL CENTRAL 1-2-3 SALA CIEPS', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quiénes', 'harán', 'parte', 'ceas', 'fiscalía'], 20);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿De qué se tratará la implementación de Frentes de Seguridad en Barrios y Veredas?', 'Estará conformado por el presidente de JAC y dos integrantes de seguridad. Fortalecimiento de los frentes de seguridad mediante capacitaciones en seguridad y convivencia Mayor efectividad en la comunicación de hechos y situaciones que afectan la seguridad y convivencia del municipio. Comisarias e inspecciones a su sector. La Casa de justicia se descentraliza y llega a los barrios y veredas del municipio de Chía', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['tratará', 'implementación', 'frentes', 'seguridad', 'barrios'], 21);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los temas de sensibilización que brinda la casa de la Justicia?', 'Pautas de Crianza, Uso adecuado de la tecnología de redes sociales. Control parental y prevención de riesgos en niños, niñas y adolescentes. Sensibilización y prevención en trabajo infantil enmarcado en el día mundial de la erradicación del Trabajo Infantil. Estudiantes de los grados 10 y 11 Colegio Nacional Diversificado. Prevención del consumo de sustancias psicoactivas y alcohol Estudiantes de los Grados 6,7,8,9,10 Institucion educativa Laura vicuña', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuáles', 'temas', 'sensibilización', 'brinda', 'casa'], 22);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Hacia quienes se dirige las campañas de prevención del consumo de sustancias psicoactivas y alcohol que brinda la casa de la Justicia?', 'A Estudiantes de los grados 8 y 11 Colegio Inmaculada', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['hacia', 'quienes', 'dirige', 'campañas', 'prevención'], 23);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿De qué se trata la estrategia OJOS EN TODAS PARTES?', 'Prevención de la explotación sexual comercial de niños niñas y adolescentes (ESCNNA) en operadores turísticos Hotel villa Juliana Hotel ibis Hotel spot', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['trata', 'estrategia', 'ojos', 'todas', 'partes'], 24);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quiénes hacen parte de la estrategia OJOS EN TODAS PARTES?', 'La Policía de turismo, La Secretaria de salud, Desarrollo Económico, Gobierno, Turismo, y la Dirección de Derechos y Resolución de Conflictos- Casa de Justicia chía, equipo psicosocial.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quiénes', 'hacen', 'parte', 'estrategia', 'ojos'], 25);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuándo se realizarán las Convocatorias para la conformación de la red Cívica de Mayores?', 'FECHAS DE CAMPAÑAS Y SENSIBILIZACIONES Campaña de Autocuidado en seguridad – 11 de octubre – Centro Chía Socializaciones de la Ley 1801 de 2016 en Propiedad Horizontal Campaña contra el reclutamiento de Grupos Ilegales – 18 de octubre Sensibilización de Consumo responsable de alcohol en gastrobares del municipio de Chía – noviembre 02 - Variante Ciclo Rural en acción – noviembre 10 – 11 “Yerbabuena y Valvanera” Protección al consumidor Para la protección y defensa de los derechos de los consumidores, es importante dar continuidad a la capacitación y orientación dirigida a los comerciantes en esta materia, con la finalidad de generar una cultura de consumo responsable, promoviendo la protección de sus propios derechos y los mecanismos que tienen para exigir su cumplimiento, brindando así un ambiente garantista.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuándo', 'realizarán', 'convocatorias', 'para', 'conformación'], 26);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la norma que protege al consumidor?', 'La Ley 1480 del 2011 y circular única de la superintendencia de industria y comercio', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'norma', 'protege', 'consumidor', 'circular'], 27);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué dependencia de la Alcaldía Municipal tiene a cargo a la protección al consumidor en el Municipio de Chía?', 'La Secretaría de Gobierno, pero solo en los casos que se trate de denuncias en contra de un establecimiento de comercio que, con sus prácticas, está que afectando a la comunidad general.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['dependencia', 'alcaldía', 'municipal', 'tiene', 'cargo'], 28);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el proceso que conoce la Secretaría de Gobierno, en caso de las quejas iniciadas por los consumidores que tengan como interés que se sancione al establecimiento de comercio?', 'Proceso administrativo sancionatorio en materia de protección al consumidor por infracciones generales.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'proceso', 'conoce', 'secretaría', 'gobierno'], 29);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuándo el interés sea particular y se quiera denunciar una mala práctica por parte de un establecimiento de comercio, ante quien se puede realizar esta diligencia?', 'Se puede dirigir a la superintendencia de industria y comercio o a una instancia judicial.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuándo', 'interés', 'particular', 'quiera', 'denunciar'], 30);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el canal de recepción de las quejas dirigidas a la superintendencia de industria y comercio?', 'Se pueden dirigir al siguiente correo electrónico <EMAIL> o acudir a la Calle 10 No 10 - 07 - Frente al parqueadero de la iglesia Santa Lucía.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'canal', 'recepción', 'quejas', 'dirigidas'], 31);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las infracciones de carácter general más comunes por parte de los establecimientos de comercio?', 'No cumplir con la publicidad de los precios – incluir cláusulas abusivas en los contratos- - brindar información general o promociones con datos inexactos, incompletos- exigir el pago de propinas - publicación de cartas en restaurante sin los requisitos exigidos para estos establecimientos de comercio- establecer en las operaciones en las que media un sistema de financiación condiciones que perjudican a los clientes- cláusulas abusivas de permanencia', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['requisito', 'cuáles', 'infracciones', 'carácter', 'general', 'comunes'], 32);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Quiero poner en conocimiento que un establecimiento de comercio no publicó el precio de un producto, como puedo proteger mis derechos como consumidor al respecto?', 'Si el interés de la denuncia es únicamente que el establecimiento de comercio sea sancionado debe remitirla a la Secretaría de Gobierno. Si el interés de la denuncia es que como cliente sea solucionado su caso particular, debe dirigir la denuncia a la superintendencia de industria y comercio.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['quiero', 'poner', 'conocimiento', 'establecimiento', 'comercio'], 33);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué documentos se exige para que la Secretaría de Gobierno atienda la solicitud de protección del consumidor?', 'Ninguno, sin embargo, Se recomienda que recaude la mayor cantidad de pruebas como factura y fotografías.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['documento', 'solicitud', 'documentos', 'exige', 'para', 'secretaría', 'gobierno'], 34);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué puedo hacer si en un establecimiento de comercio me cobraron propina obligatoria o en la carta no relacionó que la propina es voluntaria?', 'Si el interés de la denuncia es únicamente que el establecimiento de comercio sea sancionado debe remitirla a la Secretaría de Gobierno. Si el interés de la denuncia es que como cliente sea solucionado su caso particular, debe dirigir la denuncia a la superintendencia de industria y comercio.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['puedo', 'hacer', 'establecimiento', 'comercio', 'cobraron'], 35);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué hacer si en una relación comercial el establecimiento establece cláusulas de permanencia para los clientes?', 'Es común esta práctica por las empresas operadoras de telecomunicaciones – gimnasios, en este caso está clausula debe ofrecer un beneficio.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['beneficio', 'hacer', 'relación', 'comercial', 'establecimiento', 'establece'], 36);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué efecto tiene las cláusulas de permanencia ambiguas que usan los establecimientos de comercio?', 'Cualquier ambigüedad se interpreta a favor del consumidor. En caso de encontrar responsable al establecimiento de comercio de estar aplicando malas prácticas que afecta a los consumidores, ¿cuál sería la sanción que impone la secretaría de Gobierno? La sanción solo puede ser pecuniaria de hasta 100 SMMLV, si se presume que puede ser una mayor la sanción se traslada a la superintendencia de industria y comercio.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['efecto', 'tiene', 'cláusulas', 'permanencia', 'ambiguas'], 37);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la segunda instancia de la decisión tomada por la Secretaría de Gobierno?', 'La superintendencia de industria y comercio.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cuál', 'segunda', 'instancia', 'decisión', 'tomada'], 38);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo puedo saber si con base a la queja interpuesta como consumidor, la Secretaría decidió sancionar o no al establecimiento de comercio o comerciante a la que se dirigía la denuncia?', 'Se debe manifestar explícitamente en la queja, que se quiere hacer parte del proceso administrativo sancionatorio para recibir información del resultado del mismo.', 
 (SELECT id FROM faq_themes WHERE name = 'ORGANIZACIONES COMUNITARIAS'), 
 ARRAY['cómo', 'puedo', 'saber', 'base', 'queja'], 39);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la dependencia encargada de los asuntos religiosos en el Municipio de Chía?', 'La Dirección de Asuntos étnicos Religiosos y posconflictos. Cuál es la Extensión para comunicarse a la Dirección de Asuntos étnicos Religiosos y posconflictos. La extensión 032', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['cuál', 'dependencia', 'encargada', 'asuntos', 'religiosos'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Chía cuenta con política pública que incluya la diversidad Religiosa? Si , no, Cuál?', 'Soy estudiante de una Institución Educativa del Municipio de Chía, y no considero hacer parte de ninguna religión y no me siento cómodo con las exigencias de asistir a las ceremonias religiosas que organiza el colegio, cómo puedo evitar hacer parte de esas celebraciones sin que vaya a haber represalias en mi contra por parte de la I.E y de los docentes? PREGUNTAS FRECUENTES ATENCIÓN A VICTIMAS DEL CONFLICTO ARMADO:', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['chía', 'cuenta', 'política', 'pública', 'incluya'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los Requisitos para que la Alcaldía me incluya como beneficiario de la ayuda humanitaria?', 'Radicarse en el municipio de Chía 2) Hacer una declaración en la Personería Municipal 3) Tener documento de identidad 4) que el hecho victimizante no tenga más de tres meses de ocurrido. Realmente estos son los requisitos exigidos a la población desplazada para la entrega de la ayuda. También se les puede pedir una certificación bancaria (entidad bancaria o nequi) para consignarles el dinero, pero si no tienen cuenta en ningún banco o plataforma, se les entrega la ayuda en cheque. Para efectos de hacerlos beneficiarios de la oferta institucional, se les recomienda inscribirse en el SISBEN y llevar a esta entidad un recibo de servicio público. En esta Dirección lo único que exigimos es la solicitud formal de la Personería Municipal para entregar la ayuda.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['requisito', 'documento', 'solicitud', 'servicio', 'cuáles', 'requisitos', 'para', 'alcaldía'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Si he extraviado mis documentos, cómo puedo solicitar la ayuda humanitaria o que documento temporal puede servirme para realizar el trámite?', 'Con relación al caso del extravío de documentos te cuento que hay una especie de vacío. En Personería reciben la declaración de la persona así no tenga documento de identidad y ellos nos envían a la Dirección un oficio (Derecho de Petición) para que hagamos efectiva la ayuda humanitaria inmediata, pero en esa solicitud dejan constancia de que la persona es indocumentada, o sea que nos dejan la decisión de entregar o no la ayuda. Por otra parte, cuando la persona desplazada no tiene cuenta bancaria ni nequi, en Hacienda se le entrega un cheque, pero exigen la cédula para poderlo entregar, o por lo menos la contraseña. Así las cosas, lo recomendable es que el indocumentado se acerque a Registraduría para resolver este problema.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['documento', 'solicitud', 'extraviado', 'documentos', 'cómo', 'puedo', 'solicitar'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Soy Víctima del conflicto armado, como puedo hacer para ingresar a las ofertas institucionales que brinda la Alcaldía?', 'En las Secretarías de la Alcaldía Municipal de Chía hay diferentes programas, unos dirigidos a la población en general y otros a las víctimas del conflicto armado. En este caso hay que dirigirse a cada Secretaría para recibir información, Si su interés es relacionado con los servicios a la salud podría acercarse a la Secretaría de salud. (Confirmar con la dependencia a través de los enlaces cuáles son los servicios que presta a esta población) Si su interés es relacionado con los servicios a la Educación podría acercarse a la Secretaría de Educación. (Confirmar con la dependencia a través de los enlaces cuáles son los servicios que presta a esta población) Si su interés es relacionado con los servicios a xxxxxx la Secretaría de Desarrollo Social (Confirmar con la dependencia a través de los enlaces cuáles son los servicios que presta a esta población)', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['programa', 'servicio', 'víctima', 'conflicto', 'armado', 'como', 'puedo'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Soy desplazado por la violencia, que trámite debo realizar para que se me reconozca a mí y a mi núcleo familiar como víctima del conflicto armado?', 'Debe realizar una declaración ante la Personería Municipal, quienes revisaran cada caso para determinar su reconocimiento, para ello, podrá dirigir su solicitud a la siguiente dirección de correo electrónica <EMAIL>', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['solicitud', 'desplazado', 'violencia', 'trámite', 'debo', 'realizar'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Hay un número límite de personas dentro del núcleo familiar para que sean reconocidos como beneficiarios de la ayuda humanitaria?', 'Puede incluir sin límite a todos los miembros de la familia que se encuentren en condición de desplazamiento. Si como víctima del conflicto armado deseo retornar a mi lugar de origen, ¿cuál es el trámite que debe seguirse? Se debe realizar trámite ante la Unidad para las víctimas, pues son quienes evaluando las condiciones particulares autorizan o no el retorno de quien lo solicita, para lo cual puede elevar su solicitud a la siguiente dirección de correo electrónica <EMAIL> o si desea por parte de la Dirección de asuntos étnicos, raciales, religiosos y posconflicto se le puede colaborar enviando la solicitud a la Unidad para las víctimas', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['solicitud', 'servicio', 'número', 'límite', 'personas', 'dentro', 'núcleo'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es la Casa de la Justicia?', 'Es un programa que permite el acceso a la justicia donde se ofrecen servicios de información, orientación, resolución de conflictos y se aplican y ejecutan los mecanismos de justicia formal y no formal existentes en el municipio, a través de entidades del orden nacional y local. Con ellas se pretende acercar la justicia al ciudadano orientándolo sobre sus derechos, previniendo el delito, luchando contra la impunidad, facilitándole el uso de los servicios de justicia formal y promocionando la utilización de mecanismos alternativos de resolución de conflictos. Los servicios que se prestan en las casas de justicia son gratuitos', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['gratuito', 'programa', 'servicio', 'casa', 'justicia', 'permite', 'acceso'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué entidades se pueden encontrar en la Casa de la Justicia?', 'Del orden municipal Comisarías de familia Equipo Psicosocial de Comisarias de familia Centro de Recepción e Información - CRI Inspección de policía Personería municipal Conciliadores en equidad Orientación en Psicología Equipo de trabajo social y de psicología Del orden nacional Fiscalía local o seccional Inspección de trabajo Medicina Legal Policía Nacional de infancia y adolescencia Otros Centro de Conciliación en Derecho Jueces de paz y reconsideración', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['entidades', 'pueden', 'encontrar', 'casa', 'justicia'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué competencias tienen las Comisarías de Familia?', 'Las Comisarías de Familia en Chía trabajan para la protección de derechos fundamentales en el contexto de la familia, con especial incidencia cuando las víctimas son sujetos de especial protección constitucional, como son las niñas, los niños, los/las adolescentes, las mujeres y las personas mayores;
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué son las Comisarías de Familia?', 'Las Comisarías de Familia son dependencias o entidades de carácter administrativo e interdisciplinario del orden municipal, con funciones administrativas y jurisdiccionales, que tienen como objeto brindar atención especializada e interdisciplinaria para prevenir, proteger, restablecer, reparar y garantizar los derechos de quienes estén en riesgo, sean o hayan sido víctimas de violencias por razones de género y otras violencias en el contexto familiar. (Ley 2126 de 2021 artículos 2 y 3). En cumplimiento de sus funciones, las Comisarías de Familia tienen el deber de informar a la Fiscalía General de la Nación sobre hechos de violencia en el contexto familiar que conozcan, con el fin de que se investigue el delito que se configura con esta conducta. Así mismo, con sustento en el principio de subsidiariedad al no contar con sede del ICBF, también se conoce de conciliaciones de alimentos para menores y adulto mayor y de los procesos administrativos de restablecimiento de derechos de los niños.', 
 (SELECT id FROM faq_themes WHERE name = 'PRIMERA INFANCIA'), 
 ARRAY['comisarías', 'familia', 'comisarías', 'familia', 'dependencias'], 11);
