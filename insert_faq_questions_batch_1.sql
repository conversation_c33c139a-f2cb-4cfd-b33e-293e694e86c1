-- FAQ Questions Batch 1
-- Questions 1 to 50

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'Se cuenta con 26 equipos disponibles para el público, en los cuales se puede acceder al servicio de Internet gratuito. Requisitos para acceder al servicio Las personas que quieran hacer uso del servicio deben acercarse a la Biblioteca Hoqabiga de lunes a viernes en el horario de 8:00 a.m. a 5:00 p.m.', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['requisito', 'gratuito', 'horario', 'servicio', 'consiste', 'cuenta', 'equipos', 'disponibles'], 1);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'No requiere inscripción previa, el ciudadano se acerca y diligencia los datos en formato establecido', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['cómo', 'realiza', 'inscripción', 'requiere', 'inscripción'], 2);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['costo', 'tiene'], 3);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea'], 4);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'El servicio es presencial y se presta en Carrera 7 No 15 -51 Biblioteca Hoqabiga.', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['presencial', 'servicio', 'puede', 'hace'], 5);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'La Oficina de Tecnologías de Información y Comunicaciones cuenta con el personal capacitado y con las competencias para dictar cursos presenciales en diferentes áreas de la informática, tales como: Alfabetización digital con dispositivos móviles Word básico Excel básico Excel avanzado Plan de contenidos y posicionamiento de marca para redes sociales Mantenimiento de equipos de cómputo Arduino desde cero – Electrónica para niños a partir de los 8 años Arduinos II Modelado 3D Requisitos para acceder al servicio La oferta de capacitación se publica a través de las páginas oficiales de las redes sociales del punto Vive Digital y la página web de la Alcaldía Municipal. En la publicación se encuentra el link a través del cual cualquier persona que esté interesada en estos programas se pueda inscribir, además, la inscripción se puede realizar de manera presencial en el punto Vive Digital. Posterior a la inscripción, los ciudadanos reciben la información respecto a los horarios y los días de en los que se desarrolla el curso Asistir al curso y obtener el certificado', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['requisito', 'certificado', 'presencial', 'horario', 'programa', 'servicio', 'consiste', 'oficina'], 1);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'La oficina TIC genera un link a través de google – forms, en el cual los interesados se pueden inscribir, también se puede realizar e manera presencial en el punto vive digital, ubicado en la Carrera 7 No 15 -51 Biblioteca Hoqabiga', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['presencial', 'cómo', 'realiza', 'inscripción', 'oficina', 'genera'], 2);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['costo', 'tiene'], 3);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'Si, en el link que genera la oficina para cada una de las ofertas de capacitación que se realicen durante la vigencia.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea', 'link'], 4);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Si, en la Carrera 7 No 15 -51 Biblioteca Hoqabiga, Chía - Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['presencial', 'puede', 'hace', 'carrera', 'biblioteca'], 5);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste? En el Municipio de Chía se encuentran registrados 118 vendedores informales de los cuales solo 64 están dentro del programa de caracterización conforme lo establece el Decreto 187 de 2022, es decir, cantidad que obedece al 55% de esta población. (…) esta información se tomó del registro de vendedores informales (REVI) con la que cuenta la Secretaría de Gobierno, la cual se alimentó teniendo en cuenta los vendedores informales caracterizados.', 'Se hace necesario realizar una nueva caracterización teniendo en cuenta los requisitos exigidos por el Municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'registro', 'programa', 'consiste', 'municipio', 'chía', 'encuentran', 'registrados'], 1);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Requisitos para ingresar al programa de vendedores informales?', 'Según el artículo N° 187 de 2022 los requisitos para hacer parte del programa para vendedores informales deben registrar sus datos en el formato, el cual debe contener lo siguiente: 3, 4, 7, 12, 14,15 y 16, Nombres y apellidos completos. Ser mayor de edad, para lo cual se anexará copia de la cedula de ciudadanía o de extranjería Dirección de vivienda actual Certificado de vivienda, expedido por la Junta de Acción Comunal, donde conste que vive en el municipio hace más de 5 años Teléfono (fijo y/o móvil, propio o de una persona a través de la cual se le pueda contactar) Dirección de correo electrónico (propio o de una persona a través de la cal se le pueda contactar) Consulta de registro del Sisbén metodología lV o la vigente al momento de la solicitud, donde se evidencia su clasificación el cual deberá estar máximo en el grupo C o en su equivalente al momento de la solicitud Lugar donde se desarrollará su actividad informal, indicando para el caso de los vendedores semiestacionarios dirección exacta, barrio o vereda, para los vendedores ambulantes, a quienes no corresponde una dirección de venta fija, deberán reportar los tramos, sectores recorridos o puntos de referencia que enmarquen su zona de influencia. Es importante el detalle sobre el lugar de venta porque en él se realizará la verificación respectiva por parte del grupo interdisciplinario Productos y/o servicios que comercializará Periodicidad de ejecución de labor: diario, semanal o mensual Jornada y horario en la que ejerce la venta informal Declaración extra juicio donde conste que no se encuentra laborando, ni percibiendo ningún tipo de ingresos y/o pensión reconocida de fondos privados o públicos, ni salarios o sueldos o de una actividad independiente, y/o que es padre o madre cabeza de familia, o adulto mayor, en cualquier caso, sin ingreso alguno No tener vigente anotación por antecedentes policivos en el registro nacional de medidas correctivas. En caso de expender o prepara alimentos y/o bebidas en la vía pública, debe contar con un plan de capacitación en manipulación de alimentos, cuya duración de doce (12) horas, y renovarse anualmente, el cual debe estar certificado por la entidad territorial de salud o un particular autorizado que, en dicho caso, será validado por la dirección de vigilancia y control de la Secretaría de Salud del Municipio de Chía Presentar certificado médico de aptitud para manipular alimentos y resultado de laboratorios clínicos (coprológico, KOH de uñas y frotis de garganta) todos estos documentos serán validados anualmente por la Dirección de Vigilancia y Control de la Secretaría de Salud del Municipio de Chía Encontrarse afiliado al sistema de seguridad social lo cual se confirmará en la plataforma ADRESS del Ministerio de Salud y Protección Social. Este será validado por el área de aseguramiento de la Secretaría de Salud del Municipio de Chía. La ADRES y LA SECRETARIA DE SALUD, NO emiten certificados de afiliaciones, Las EPS son las únicas responsables de generar estas certificaciones, consulte a través del botón “consulte su EPS” en el enlace https://www.adres.gov.co/consulte-su-eps o la que haga sus veces. Para obtener el certificado de afiliación debe acudir a la EPS en donde se encuentra afiliado.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'documento', 'certificado', 'registro', 'solicitud', 'horario', 'programa', 'servicio'], 2);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Además de acreditar que el valor del inmueble de interés social VIS o vivienda de interés social prioritario VIP, no supera el tope máximo fijado, ¿que otro requisito en relación con el bien debo cumplir para poder acceder al programa de registro como vendedor informal?', 'Se debe acreditar que vive en el inmueble.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'registro', 'programa', 'además', 'acreditar', 'valor', 'inmueble', 'interés'], 3);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se acredita que la propiedad que tengo es un beneficio de la ley 1448 de 2011?', 'Debe allegar el registro de la propiedad expedido por la Unidad Administrativa Especial de Gestión de Restitución de tierra Despojadas', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'beneficio', 'cómo', 'acredita', 'propiedad', 'tengo'], 4);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se puede acreditar la nuda propiedad sobre un bien inmueble y estoy interesado en acceder al programa de registro como vendedor informal?', 'Deberá acreditarlo allegando el certificado de tradición y libertad con fecha de expedición menor a tres (3) meses.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['certificado', 'registro', 'programa', 'cómo', 'puede', 'acreditar', 'nuda', 'propiedad'], 5);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Si soy propietario de varios bienes inmuebles, puedo acceder al programa de registro como vendedor informal?', 'No, Solo se tramitará la solicitud de aquella persona que tenga una única propiedad en cualquiera de las situaciones antes anotadas', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'solicitud', 'programa', 'propietario', 'varios', 'bienes', 'inmuebles', 'puedo'], 6);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál sería la consecuencia en caso de llegar a verificarse que el vendedor informal es propietario de un vehículo automotor?', 'Este hecho será suficiente motivación para dar respuesta desfavorable de ingreso al registro de vendedores informales de Chía- REVI', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'cuál', 'sería', 'consecuencia', 'caso', 'llegar'], 7);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué casos se prioriza el ingreso, caracterización y registro a los vendedores informales que además de cumplir los anteriores requisitos?', 'Quienes se encuentren y acrediten algunas de las condiciones de vulnerabilidad de las definidas por la Corte Constitucional, como son: Ser persona en discapacidad o tener a cargo una persona en condición de discapacidad (física, sensorial o cognitiva funcional) presentando el CERTIFICADO DE DISCAPACIDAD, emitido por la Secretaria de Salud según normativa vigente. Ser víctima del conflicto armado para lo cual anexará el registro único de victimas (RUV) o certificado emitido por parte de la Personería Municipal de Chía Pertenecer a una minoría étnica reconocida por el Ministerio del Interior anexando certificado emitido ya sea por el Gobernador Indígena del Resguardo de Chía o por el Ministerio del Interior Ser madre o padre cabeza de familia o adulto mayor sin ingreso alguno, para lo cual anexará juicio que exprese esta condición Identificarse como parte de la población LGBTIQ+ con aplicación del enfoque diferencial con el fin de garantizar ciertos derechos', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'certificado', 'registro', 'casos', 'prioriza', 'ingreso', 'caracterización'], 8);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En el caso en que quiera renovar el registro de vendedores informales de Chía REVI, debo allegar todos los documentos requeridos para la inscripción inicial?', 'No, solo deberá allegar los siguientes documentos Dirección de vivienda actual Certificado de vivienda, expedido por la Junta de Acción Comunal, donde conste que vive en el municipio hace más de 5 años. Consulta de registro del Sisbén metodología lV o la vigente al momento de la solicitud, donde se evidencia su clasificación el cual deberá estar máximo en el grupo C o en su equivalente al momento de la solicitud Declaración extra juicio donde conste que no se encuentra laborando, ni percibiendo ningún tipo de ingresos y/o pensión reconocida de fondos privados o públicos, ni salarios o sueldos o de una actividad independiente, y/o que es padre o madre cabeza de familia, o adulto mayor, en cualquier caso, sin ingreso alguno. En caso de expender o prepara alimentos y/o bebidas en la vía pública, debe contar con un plan de capacitación en manipulación de alimentos, cuya duración de doce (12) horas, y renovarse anualmente, el cual debe estar certificado por la entidad territorial de salud o un particular autorizado que, en dicho caso, será validado por la dirección de vigilancia y control de la Secretaría de Salud del Municipio de Chía Presentar certificado médico de aptitud para manipular alimentos y resultado de laboratorios clínicos (coprológico, KOH de uñas y frotis de garganta) todos estos documentos serán validados anualmente por la Dirección de Vigilancia y Control de la Secretaría de Salud del Municipio de Chía Encontrarse afiliado al sistema de seguridad social lo cual se confirmará en la plataforma ADRESS del Ministerio de Salud y Protección Social. Este será validado por el área de aseguramiento de la Secretaría de Salud del Municipio de Chía. La ADRES y LA SECRETARIA DE SALUD, NO emiten certificados de afiliaciones, Las EPS son las únicas responsables de generar estas certificaciones, consulte a través del botón “consulte su EPS” en el enlace https://www.adres.gov.co/consulte-su-eps o la que haga sus veces. Para obtener el certificado de afiliación debe acudir a la EPS en donde se encuentra afiliado.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['documento', 'certificado', 'registro', 'solicitud', 'caso', 'quiera', 'renovar', 'vendedores'], 9);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'La solicitud de ingreso debe presentarse entre los meses de febrero y marzo, de cada año, así:', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['solicitud', 'cómo', 'realiza', 'inscripción', 'ingreso'], 10);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el término para realizar la solicitud de registro como vendedor ambulante?', 'TERMINO PAA REALIZAR LA SOLICITUD: La solicitud deberá realizarse durante los meses de febrero y marzo de cada anualidad, siendo esa la única oportunidad para recibir y tramitar la solicitud.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'solicitud', 'cuál', 'término', 'para', 'realizar'], 11);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es el propósito del registro como vendedor informal?', 'Que se cuente con la caracterización y el registro del vendedor informal dentro del Municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'cuál', 'propósito', 'como', 'vendedor'], 12);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la consecuencia si no alcanzo a presentar mi solicitud de registro como vendedor informal dentro de los meses de febrero y marzo de cada anualidad?', 'Las solicitudes que se presenten por fuera de este término serán rechazadas de plano y no serán tenidas en cuenta por ser extemporáneas, en consideración a qué el registro es temporal y, por lo tanto, tendrá vigencia de un (1) año.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'solicitud', 'cuál', 'consecuencia', 'alcanzo', 'presentar'], 13);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo el registro, trámite, caracterización y carnetización del REVI?', 'El registro, tramite, caracterización y carnetización del REVI NO, ningún tipo de costo, el carnet será expedido por la Secretaría de Gobierno del Municipio de Chía.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['tramite', 'registro', 'costo', 'tiene', 'trámite', 'caracterización'], 14);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la vigencia del registro como vendedor informal?', 'Tendrá vigencia de un (1) año contado a partir de la entrega al vendedor informal.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'cuál', 'vigencia', 'como', 'vendedor'], 15);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se identifican los Vendedores informales registrados?', 'Se identifican con un carné, el cual se entregará por persona del núcleo familiar.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['cómo', 'identifican', 'vendedores', 'informales', 'registrados'], 16);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Una vez transcurrido el año de vigencia del registro como vendedor informal debo devolver el carné?', 'Sí, el vendedor informal deberá devolver el carnet vencido. Vencido el registro como vendedor informal, ¿cuál es la gestión que debo realizar para continuar desempeñando mis labores? Se debe realizar una solicitud de actualización del Registro de Vendedores Informales de Chía- REVI, luego de lo cual se le hará entrega de un nuevo carné.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'solicitud', 'transcurrido', 'vigencia', 'como', 'vendedor'], 17);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'La inscripción al REVI se puede realizar de manera virtual, enviando un correo electrónico a contactenos@chía.gov.co enviando en el mismo una petición por escrito, bajo la gravedad de juramento, adjuntando los soportes correspondientes y documentos ya mencionados.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['documento', 'virtual', 'inscripción', 'puede', 'hacer', 'línea', 'inscripción'], 18);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Se puede realizar la solicitud de manera presencial acercándose a la Secretaría de Gobierno del Municipio de Chía, que se encuentra ubicada en la carrera 7° #12-100 Chía, Cundinamarca, allegando una petición por escrito, bajo la gravedad de juramento, adjuntando los soportes correspondientes y documentos ya mencionados.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['documento', 'solicitud', 'presencial', 'puede', 'hace', 'puede', 'realizar'], 19);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué ventajas tienen la tecnología y las redes sociales?', 'Los avances tecnológicos innovan y crean nuevos productos y sistemas Ayudan a estar en contacto con amigos y familia Se logra una comunicación rápida mediante mensajes Se puede estar actualizado en las noticias y eventos Internet puede ser una herramienta de búsqueda de temas de interés. La tecnología tiene una variedad de opciones de entretenimiento Son útiles para buscar oportunidades de trabajo Puedes tomar fotos y grabar videos', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['ventajas', 'tienen', 'tecnología', 'redes', 'sociales'], 1);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la edad apropiada para darle acceso a la tecnología a niños, niñas y adolescentes?', '“Pese a la familiaridad de los niños, niñas y adolescentes con la tecnología, algunos expertos y comunidades científicas han llamado la atención acerca de la importancia de dar acceso a los dispositivos y herramientas digitales, en el momento apropiado y de acuerdo con el momento de su ciclo de vida. Esto con tres propósitos esenciales: En primer lugar, reducir los efectos negativos que puede ocasionar el uso de pantallas en la primera infancia; segundo, permitir que los menores de edad puedan aprovechar realmente todas las ventajas que ofrecen las nuevas tecnologías a partir del grado de desarrollo de sus habilidades cognitivas; y, por último, reducir la exposición a los riesgos digitales a temprana edad”*. https://www.icbf.gov.co/mis-manos-te-ensenan/que-edad-puedo-darle-un-celular-una-tableta-o-acceso-internet-un-nino', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['cuál', 'edad', 'apropiada', 'para', 'darle'], 2);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es la edad apropiada para darle acceso a la tecnología a niños, niñas y adolescentes? *', 'Antes de 3 años: Cero pantallas Antes de 6 años: Nada de videojuegos Antes de 9 años: Acceso a dispositivos tecnológicos sin internet Antes de 13 años: Dispositivos tecnológicos con conexión a internet sin redes sociales. Después de 13 años: Acceso a dispositivos tecnológicos con conexión a internet y con redes sociales. *https://www.icbf.gov.co/mis-manos-te-ensenan/que-edad-puedo-darle-un-celular-una-tableta-o-acceso-internet-un-nino', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['cuál', 'edad', 'apropiada', 'para', 'darle'], 3);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los riesgos asociados con el uso inadecuado de la tecnología y redes sociales?', 'Ciberacoso: “Según la Unicef, el ciberacoso es acoso o intimidación por medio de las tecnologías digitales. Puede ocurrir en las redes sociales, las plataformas de mensajería, las plataformas de juegos y los teléfonos móviles. Es un comportamiento que se repite y que busca atemorizar, enfadar o humillar a otras personas” Sexting: “Consiste en que voluntariamente la persona produce, publica, envía mensajes, fotos y videos con contendido sexualmente explícito de sí mismo, por medio de teléfonos celulares o cualquier otro medio electrónico. En sí, el “sexting” no implica una infracción como tal, sin embargo, cuando se divulgan públicamente las fotos o videos sin el consentimiento del involucrado, se puede llegar a incurrir en delitos tales como: extorsión, pornografía infantil, prostitución infantil, entre otros, los cuales está en el deber de denunciar ante la autoridad correspondiente” *. Si se involucran Niños, Niñas y Adolescentes se estaría inmerso en el delito pornografía – Información Ministerio de Justicia y del Derecho. Grooming: “Es una nueva forma de acoso y abuso hacia niños, jóvenes que se ha venido popularizando con el auge de las TIC, principalmente los chats y redes sociales. Inicia con una simple conversación virtual, en la que el adulto se hace pasar por otra persona, normalmente, por una de la misma edad de niño con el objetivo de obtener una satisfacción sexual mediante imágenes eróticas o pornográficas del menor o incluso como preparación para un encuentro sexual.” * – Información MinTIC-Colombia​ Adicción a redes: Según la Organización Mundial de la Salud (OMS) el uso excesivo de las tecnologías o Internet tiene la misma consecuencia que una sustancia psicoactiva. De esta forma ha determinado que la adicción a Internet y el uso excesivo de las nuevas tecnologías como smartphones o tablets y redes sociales, entre otros; se califica ya como enfermedad.', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['virtual', 'cuáles', 'riesgos', 'asociados', 'inadecuado', 'tecnología'], 4);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué Herramientas se tienen para el uso adecuado de la tecnología y redes sociales?', 'Importancia de establecer límites y reglas Proteger la seguridad y privacidad de los hijos Fomentar un uso saludable y equilibrado de la tecnología Evitar adicciones y distracciones Promover la responsabilidad y autonomía Tips para establecer límites y reglas efectivas Establecer reglas claras y específicas Comunicar las reglas de manera clara y abierta Establecer consecuencias por incumplimiento Monitorear y supervisar el uso de tecnología Revisar y actualizar las reglas periódicamente Consejos adicionales Ser un modelo a seguir Fomentar la comunicación abierta Educar sobre seguridad en línea y privacidad Establecer un acuerdo de uso de tecnología', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['herramientas', 'tienen', 'para', 'adecuado', 'tecnología'], 5);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué es el control parental?', 'Es una herramienta que permite a las familias supervisar y adecuar el tiempo de uso y contenido al que tienen acceso sus hijos. Normativa y Responsabilidad Penal El Código Penal colombiano, que prohíbe conductas como el ciberacoso, la difamación y la injuria. La Ley 1620 de 2013, que establece el Sistema Nacional de Convivencia Escolar y Formación para el Ejercicio de los Derechos Humanos, la Educación para la Sexualidad y la Prevención y Mitigación de la Violencia Escolar. La cual estipula que el ciberacoso es una forma de violencia escolar y por lo tanto se considera una falta disciplinaria. El Código Penal colombiano establece penas de prisión de tres meses a dos años para quien cometa actos de acoso o hostigamiento a través de medios electrónicos. Información - Miconsultoriojuridico.co', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['control', 'parental', 'herramienta', 'permite', 'familias'], 6);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son las consecuencias legales del mal uso de las redes sociales?', 'Penas de prisión de tres meses a dos años por ciberacoso. Multas de 6 a 24 meses por ciberacoso. Expulsión de la institución educativa en el caso de que el ciberacoso se produzca en un entorno escolar. Daños y perjuicios por difamación o injuria. Inhabilitación para el ejercicio de cargos públicos.', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['cuáles', 'consecuencias', 'legales', 'redes', 'sociales'], 7);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En dónde denunciar por ciberacoso?', 'Colegio (Comité de Convivencia) Comisaría de Familia Policía Nacional Línea 141 ICBF Fiscalía', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['dónde', 'denunciar', 'ciberacoso', 'colegio', 'comité'], 8);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál es las líneas de ayuda psicosocial?', 'La línea 106 el poder de ser escuchado,', 
 (SELECT id FROM faq_themes WHERE name = 'FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS'), 
 ARRAY['cuál', 'líneas', 'ayuda', 'psicosocial', 'línea'], 9);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los servicios que presta el equipo de Gestión del Riesgo?', 'Gestiona, administra, direcciona y monitorea diferentes escenarios de riesgo que se puedan presentar ante emergencias inminentes y no inminentes. También brindamos capacitaciones, charlas y asistencias técnicas.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['direccion', 'servicio', 'asistencia', 'cuáles', 'servicios', 'presta', 'equipo', 'gestión'], 1);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En dónde puedo reportar una emergencia?', 'Se debe realizar exclusivamente a la línea de atención 123 Ante un supuesto riesgo (no inminente): Hay un árbol fuera/dentro de mi casa y/o predio, que está en riesgo de caída. ¿Qué debo hacer? Debe realizar la solicitud que incluya sus datos de contacto, ubicación urbana o rural.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['solicitud', 'dónde', 'puedo', 'reportar', 'emergencia', 'debe'], 2);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtendré la respuesta?', 'Al término de quince días hábiles se habrá atendido su solicitud. Hay un(os) panal(es) de abejas en el lugar “x”, deseo que lo(s) retiren, ¿Qué debo hacer para tramitar mi solicitud? Por favor ingrese su nombre completo Número y tipo de identificación Ubicación del lugar Trasladaremos la petición al área encargada Encontré una zarigüeya en uno de los arbustos de mi jardín, ¿Qué hago para que la recojan? La misma respuesta que en la pregunta No. 2 Otras inquietudes que podrían tener los ciudadanos, vinculadas a las líneas de atención que maneja éste equipo de GRD: Quisiera que me agenden una capacitación en primeros auxilios, ¿Cuál es la ruta, los requisitos?', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['requisito', 'solicitud', 'cuánto', 'tiempo', 'obtendré', 'respuesta', 'término'], 3);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿A quién va dirigida la capacitación pregunta IA?: otra dependencia, institución educativa, colegio, otros. Elija una opción', 'Ingrese el número de personas a las cuáles va dirigida la capacitación Al término de quince días hábiles se dará respuesta a su solicitud.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['solicitud', 'quién', 'dirigida', 'capacitación', 'pregunta', 'otra'], 4);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué requisitos se deben cumplir para realizar un evento en el municipio?', 'Antes de la radicación del evento se deben adelantar estos requisitos: Debe presentar el plan de emergencia y contingencia <NAME_EMAIL> para que por parte de la Dirección Centro de Atención al Ciudadano se remita a bomberos y estos agenden la inspección, posteriormente remitir el informe de la inspección que adelantó Bomberos al correo <NAME_EMAIL> para que por parte de la Dirección Centro de Atención al Ciudadano se remita a la Secretaría de Gobierno – Gestión del Riesgo – Comité de Conocimiento y Prevención del Riesgo Para Eventos Masivos y No Masivos y así obtener el concepto favorable. Solicitar concepto favorable de la Secretaría de Salud diligenciando este link: https://forms.office.com/r/cuiu22nmVw Obtener concepto favorable de la Secretaría de Movilidad de Chía, radicando el Plan de Manejo de Tránsito (PMT) a <EMAIL> Enviar solicitud a la Policía Nacional para acompañamiento y control del evento en caso de ser necesario <NAME_EMAIL> Si el evento tiene recorrido, desfiles y/o similares, solicitar por escrito el permiso del evento y obtener respuesta de autorización por parte de la Secretaría de Movilidad. Si el evento es en sitio, presentar el Plan de Manejo de Tránsito (PMT) a la Secretaría de Tránsito y obtener el concepto favorable con anticipación. SI EN EL EVENTO SE LLEVARÁ A CABO RECORRIDO Y EVENTO EN SITIO SE DEBERÁN TRAMITAR LOS DOS PERMISOS – (permisos de los numerales 3 y 5). Cuando ya cuente con las anteriores autorizaciones, se radicará el trámite ante el Comité de Eventos como mínimo con un (1) mes antes de la realización del evento. La documentación que debes presentar es: Cédula del Representante Legal Plan de Emergencia y Contingencia Carta de intención del evento Concepto Favorable de la inspección adelantada por Bomberos Concepto Favorable de la inspección adelantada por Secretaría de Salud Concepto Favorable de la Inspección adelantada por Secretaría de Movilidad Presentar Paz y Salvo de pago de Derechos de Autor autorizadas por la Dirección Nacional de Derechos de Autor Póliza de Responsabilidad Civil Extracontractual que ampare la seguridad de las personas y de los elementos Anexar pantallazo de la solicitud a la Policía Nacional, en caso de requerirlo Si el evento es de espectáculos públicos, es decir, música, danza, circo sin animales, teatro, magia, debes presentar adicional: Certificados de contribución parafiscal Certificado de productor Ficha PULEP En caso de edificaciones nuevas, contar con un Concepto Técnico e Comportamiento Estructural y Funcional del escenario. NOTA: Se entenderá como radicado del evento un (1) solo correo que recopile toda la documentación requerida.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['requisito', 'certificado', 'solicitud', 'requisitos', 'deben', 'cumplir', 'para', 'realizar'], 5);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtengo la autorización?', 'El tiempo de respuesta para este trámite es de máximo quince (15) días.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['cuánto', 'tiempo', 'obtengo', 'autorización', 'tiempo'], 6);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Qué debo hacer para radicar la solicitud?', 'Una vez reunida toda la documentación requerida, puedes radicar la solicitud en la ventanilla o <NAME_EMAIL>', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['solicitud', 'debo', 'hacer', 'para', 'radicar'], 7);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene algún costo esta autorización?', 'NO TIENE COSTO', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:'), 
 ARRAY['costo', 'tiene', 'algún', 'esta', 'autorización'], 8);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuáles son los servicios que presta el equipo de Gestión del Riesgo?', 'Gestiona, administra, direcciona y monitorea diferentes escenarios de riesgo que se puedan presentar ante emergencias inminentes y no inminentes. También brindamos capacitaciones, charlas y asistencias técnicas.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios enfocados en reducir, mitigar, adelantar, contribuir y dar respuesta:'), 
 ARRAY['direccion', 'servicio', 'asistencia', 'cuáles', 'servicios', 'presta', 'equipo', 'gestión'], 1);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En dónde puedo reportar una emergencia?', 'Se debe realizar exclusivamente a la línea de atención 123 Ante un supuesto riesgo (no inminente): Hay un árbol fuera/dentro de mi casa y/o predio, que está en riesgo de caída. ¿Qué debo hacer? Debe realizar la solicitud que incluya sus datos de contacto, ubicación urbana o rural.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios enfocados en reducir, mitigar, adelantar, contribuir y dar respuesta:'), 
 ARRAY['solicitud', 'dónde', 'puedo', 'reportar', 'emergencia', 'debe'], 2);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En cuánto tiempo obtendré la respuesta?', 'Al término de quince días hábiles se habrá atendido su solicitud. Hay un(os) panal(es) de abejas en el lugar “x”, deseo que lo(s) retiren, ¿Qué debo hacer para tramitar mi solicitud? Por favor ingrese su nombre completo Número y tipo de identificación Ubicación del lugar Trasladaremos la petición al área encargada Encontré una zarigüeya en uno de los arbustos de mi jardín, ¿Qué hago para que la recojan? La misma respuesta que en la pregunta No. 2 Otras inquietudes que podrían tener los ciudadanos, vinculadas a las líneas de atención que maneja éste equipo de GRD: Quisiera que me agenden una capacitación en primeros auxilios, ¿Cuál es la ruta, los requisitos?', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios enfocados en reducir, mitigar, adelantar, contribuir y dar respuesta:'), 
 ARRAY['requisito', 'solicitud', 'cuánto', 'tiempo', 'obtendré', 'respuesta', 'término'], 3);

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿A quién va dirigida la capacitación pregunta IA?: otra dependencia, institución educativa, colegio, otros. Elija una opción', 'Ingrese el número de personas a las cuáles va dirigida la capacitación Al término de quince días hábiles se dará respuesta a su solicitud.', 
 (SELECT id FROM faq_themes WHERE name = 'La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios enfocados en reducir, mitigar, adelantar, contribuir y dar respuesta:'), 
 ARRAY['solicitud', 'quién', 'dirigida', 'capacitación', 'pregunta', 'otra'], 4);
