-- FAQ Chunk 1
-- Questions 1 to 20

INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'Se cuenta con 26 equipos disponibles para el público, en los cuales se puede acceder al servicio de Internet gratuito. Requisitos para acceder al servicio Las personas que quieran hacer uso del servicio deben acercarse a la Biblioteca Hoqabiga de lunes a viernes en el horario de 8:00 a.m. a 5:00 p.m.', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['requisito', 'gratuito', 'horario', 'servicio', 'consiste', 'cuenta', 'equipos', 'disponibles'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'No requiere inscripción previa, el ciudadano se acerca y diligencia los datos en formato establecido', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['cómo', 'realiza', 'inscripción', 'requiere', 'inscripción'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['costo', 'tiene'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'El servicio es presencial y se presta en Carrera 7 No 15 -51 Biblioteca Hoqabiga.', 
 (SELECT id FROM faq_themes WHERE name = 'INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO'), 
 ARRAY['presencial', 'servicio', 'puede', 'hace'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste?', 'La Oficina de Tecnologías de Información y Comunicaciones cuenta con el personal capacitado y con las competencias para dictar cursos presenciales en diferentes áreas de la informática, tales como: Alfabetización digital con dispositivos móviles Word básico Excel básico Excel avanzado Plan de contenidos y posicionamiento de marca para redes sociales Mantenimiento de equipos de cómputo Arduino desde cero – Electrónica para niños a partir de los 8 años Arduinos II Modelado 3D Requisitos para acceder al servicio La oferta de capacitación se publica a través de las páginas oficiales de las redes sociales del punto Vive Digital y la página web de la Alcaldía Municipal. En la publicación se encuentra el link a través del cual cualquier persona que esté interesada en estos programas se pueda inscribir, además, la inscripción se puede realizar de manera presencial en el punto Vive Digital. Posterior a la inscripción, los ciudadanos reciben la información respecto a los horarios y los días de en los que se desarrolla el curso Asistir al curso y obtener el certificado', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['requisito', 'certificado', 'presencial', 'horario', 'programa', 'servicio', 'consiste', 'oficina'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'La oficina TIC genera un link a través de google – forms, en el cual los interesados se pueden inscribir, también se puede realizar e manera presencial en el punto vive digital, ubicado en la Carrera 7 No 15 -51 Biblioteca Hoqabiga', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['presencial', 'cómo', 'realiza', 'inscripción', 'oficina', 'genera'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Tiene costo?', 'No', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['costo', 'tiene'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿La inscripción se puede hacer en línea?', 'Si, en el link que genera la oficina para cada una de las ofertas de capacitación que se realicen durante la vigencia.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['inscripción', 'puede', 'hacer', 'línea', 'link'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Se puede hace presencial?', 'Si, en la Carrera 7 No 15 -51 Biblioteca Hoqabiga, Chía - Cundinamarca.', 
 (SELECT id FROM faq_themes WHERE name = 'PROGRAMA DE CAPACITACIÓN DIGITAL'), 
 ARRAY['presencial', 'puede', 'hace', 'carrera', 'biblioteca'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué consiste? En el Municipio de Chía se encuentran registrados 118 vendedores informales de los cuales solo 64 están dentro del programa de caracterización conforme lo establece el Decreto 187 de 2022, es decir, cantidad que obedece al 55% de esta población. (…) esta información se tomó del registro de vendedores informales (REVI) con la que cuenta la Secretaría de Gobierno, la cual se alimentó teniendo en cuenta los vendedores informales caracterizados.', 'Se hace necesario realizar una nueva caracterización teniendo en cuenta los requisitos exigidos por el Municipio.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'registro', 'programa', 'consiste', 'municipio', 'chía', 'encuentran', 'registrados'], 1);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Requisitos para ingresar al programa de vendedores informales?', 'Según el artículo N° 187 de 2022 los requisitos para hacer parte del programa para vendedores informales deben registrar sus datos en el formato, el cual debe contener lo siguiente: 3, 4, 7, 12, 14,15 y 16, Nombres y apellidos completos. Ser mayor de edad, para lo cual se anexará copia de la cedula de ciudadanía o de extranjería Dirección de vivienda actual Certificado de vivienda, expedido por la Junta de Acción Comunal, donde conste que vive en el municipio hace más de 5 años Teléfono (fijo y/o móvil, propio o de una persona a través de la cual se le pueda contactar) Dirección de correo electrónico (propio o de una persona a través de la cal se le pueda contactar) Consulta de registro del Sisbén metodología lV o la vigente al momento de la solicitud, donde se evidencia su clasificación el cual deberá estar máximo en el grupo C o en su equivalente al momento de la solicitud Lugar donde se desarrollará su actividad informal, indicando para el caso de los vendedores semiestacionarios dirección exacta, barrio o vereda, para los vendedores ambulantes, a quienes no corresponde una dirección de venta fija, deberán reportar los tramos, sectores recorridos o puntos de referencia que enmarquen su zona de influencia. Es importante el detalle sobre el lugar de venta porque en él se realizará la verificación respectiva por parte del grupo interdisciplinario Productos y/o servicios que comercializará Periodicidad de ejecución de labor: diario, semanal o mensual Jornada y horario en la que ejerce la venta informal Declaración extra juicio donde conste que no se encuentra laborando, ni percibiendo ningún tipo de ingresos y/o pensión reconocida de fondos privados o públicos, ni salarios o sueldos o de una actividad independiente, y/o que es padre o madre cabeza de familia, o adulto mayor, en cualquier caso, sin ingreso alguno No tener vigente anotación por antecedentes policivos en el registro nacional de medidas correctivas. En caso de expender o prepara alimentos y/o bebidas en la vía pública, debe contar con un plan de capacitación en manipulación de alimentos, cuya duración de doce (12) horas, y renovarse anualmente, el cual debe estar certificado por la entidad territorial de salud o un particular autorizado que, en dicho caso, será validado por la dirección de vigilancia y control de la Secretaría de Salud del Municipio de Chía Presentar certificado médico de aptitud para manipular alimentos y resultado de laboratorios clínicos (coprológico, KOH de uñas y frotis de garganta) todos estos documentos serán validados anualmente por la Dirección de Vigilancia y Control de la Secretaría de Salud del Municipio de Chía Encontrarse afiliado al sistema de seguridad social lo cual se confirmará en la plataforma ADRESS del Ministerio de Salud y Protección Social. Este será validado por el área de aseguramiento de la Secretaría de Salud del Municipio de Chía. La ADRES y LA SECRETARIA DE SALUD, NO emiten certificados de afiliaciones, Las EPS son las únicas responsables de generar estas certificaciones, consulte a través del botón “consulte su EPS” en el enlace https://www.adres.gov.co/consulte-su-eps o la que haga sus veces. Para obtener el certificado de afiliación debe acudir a la EPS en donde se encuentra afiliado.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'documento', 'certificado', 'registro', 'solicitud', 'horario', 'programa', 'servicio'], 2);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Además de acreditar que el valor del inmueble de interés social VIS o vivienda de interés social prioritario VIP, no supera el tope máximo fijado, ¿que otro requisito en relación con el bien debo cumplir para poder acceder al programa de registro como vendedor informal?', 'Se debe acreditar que vive en el inmueble.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'registro', 'programa', 'además', 'acreditar', 'valor', 'inmueble', 'interés'], 3);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se acredita que la propiedad que tengo es un beneficio de la ley 1448 de 2011?', 'Debe allegar el registro de la propiedad expedido por la Unidad Administrativa Especial de Gestión de Restitución de tierra Despojadas', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'beneficio', 'cómo', 'acredita', 'propiedad', 'tengo'], 4);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se puede acreditar la nuda propiedad sobre un bien inmueble y estoy interesado en acceder al programa de registro como vendedor informal?', 'Deberá acreditarlo allegando el certificado de tradición y libertad con fecha de expedición menor a tres (3) meses.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['certificado', 'registro', 'programa', 'cómo', 'puede', 'acreditar', 'nuda', 'propiedad'], 5);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Si soy propietario de varios bienes inmuebles, puedo acceder al programa de registro como vendedor informal?', 'No, Solo se tramitará la solicitud de aquella persona que tenga una única propiedad en cualquiera de las situaciones antes anotadas', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'solicitud', 'programa', 'propietario', 'varios', 'bienes', 'inmuebles', 'puedo'], 6);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cuál sería la consecuencia en caso de llegar a verificarse que el vendedor informal es propietario de un vehículo automotor?', 'Este hecho será suficiente motivación para dar respuesta desfavorable de ingreso al registro de vendedores informales de Chía- REVI', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['registro', 'cuál', 'sería', 'consecuencia', 'caso', 'llegar'], 7);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En qué casos se prioriza el ingreso, caracterización y registro a los vendedores informales que además de cumplir los anteriores requisitos?', 'Quienes se encuentren y acrediten algunas de las condiciones de vulnerabilidad de las definidas por la Corte Constitucional, como son: Ser persona en discapacidad o tener a cargo una persona en condición de discapacidad (física, sensorial o cognitiva funcional) presentando el CERTIFICADO DE DISCAPACIDAD, emitido por la Secretaria de Salud según normativa vigente. Ser víctima del conflicto armado para lo cual anexará el registro único de victimas (RUV) o certificado emitido por parte de la Personería Municipal de Chía Pertenecer a una minoría étnica reconocida por el Ministerio del Interior anexando certificado emitido ya sea por el Gobernador Indígena del Resguardo de Chía o por el Ministerio del Interior Ser madre o padre cabeza de familia o adulto mayor sin ingreso alguno, para lo cual anexará juicio que exprese esta condición Identificarse como parte de la población LGBTIQ+ con aplicación del enfoque diferencial con el fin de garantizar ciertos derechos', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['requisito', 'certificado', 'registro', 'casos', 'prioriza', 'ingreso', 'caracterización'], 8);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿En el caso en que quiera renovar el registro de vendedores informales de Chía REVI, debo allegar todos los documentos requeridos para la inscripción inicial?', 'No, solo deberá allegar los siguientes documentos Dirección de vivienda actual Certificado de vivienda, expedido por la Junta de Acción Comunal, donde conste que vive en el municipio hace más de 5 años. Consulta de registro del Sisbén metodología lV o la vigente al momento de la solicitud, donde se evidencia su clasificación el cual deberá estar máximo en el grupo C o en su equivalente al momento de la solicitud Declaración extra juicio donde conste que no se encuentra laborando, ni percibiendo ningún tipo de ingresos y/o pensión reconocida de fondos privados o públicos, ni salarios o sueldos o de una actividad independiente, y/o que es padre o madre cabeza de familia, o adulto mayor, en cualquier caso, sin ingreso alguno. En caso de expender o prepara alimentos y/o bebidas en la vía pública, debe contar con un plan de capacitación en manipulación de alimentos, cuya duración de doce (12) horas, y renovarse anualmente, el cual debe estar certificado por la entidad territorial de salud o un particular autorizado que, en dicho caso, será validado por la dirección de vigilancia y control de la Secretaría de Salud del Municipio de Chía Presentar certificado médico de aptitud para manipular alimentos y resultado de laboratorios clínicos (coprológico, KOH de uñas y frotis de garganta) todos estos documentos serán validados anualmente por la Dirección de Vigilancia y Control de la Secretaría de Salud del Municipio de Chía Encontrarse afiliado al sistema de seguridad social lo cual se confirmará en la plataforma ADRESS del Ministerio de Salud y Protección Social. Este será validado por el área de aseguramiento de la Secretaría de Salud del Municipio de Chía. La ADRES y LA SECRETARIA DE SALUD, NO emiten certificados de afiliaciones, Las EPS son las únicas responsables de generar estas certificaciones, consulte a través del botón “consulte su EPS” en el enlace https://www.adres.gov.co/consulte-su-eps o la que haga sus veces. Para obtener el certificado de afiliación debe acudir a la EPS en donde se encuentra afiliado.', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['documento', 'certificado', 'registro', 'solicitud', 'caso', 'quiera', 'renovar', 'vendedores'], 9);
INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('¿Cómo se realiza la inscripción?', 'La solicitud de ingreso debe presentarse entre los meses de febrero y marzo, de cada año, así:', 
 (SELECT id FROM faq_themes WHERE name = 'Espacio Público'), 
 ARRAY['solicitud', 'cómo', 'realiza', 'inscripción', 'ingreso'], 10);
