"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/faq/FAQSection.tsx":
/*!***************************************!*\
  !*** ./components/faq/FAQSection.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQSection: function() { return /* binding */ FAQSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,ChevronDown,ChevronUp,CreditCard,FileCheck,FileText,Filter,HelpCircle,Receipt,Search,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/faqService */ \"(app-pages-browser)/./lib/services/faqService.ts\");\n/* harmony import */ var _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/faqAnalytics */ \"(app-pages-browser)/./lib/services/faqAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ FAQSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * Mapeo de iconos para categorías\n */ const categoryIcons = {\n    Receipt: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    FileCheck: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Award: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    Zap: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    FileText: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    CreditCard: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    HelpCircle: _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\n/**\n * Componente principal de la sección FAQ\n */ function FAQSection(param) {\n    let { title = \"Preguntas Frecuentes\", description = \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre tr\\xe1mites y servicios municipales\", initialLimit = 10, showSearch = true, showCategoryFilter = true, showStats = true, className = \"\" } = param;\n    _s();\n    // Estados\n    const [faqs, setFaqs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedFAQs, setExpandedFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAllFAQs, setShowAllFAQs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalFAQs: 0,\n        totalCategories: 0,\n        averagePopularity: 0,\n        mostPopularCategory: \"\"\n    });\n    // Cargar datos iniciales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInitialData();\n    }, []);\n    // Realizar búsqueda cuando cambie la query o categoría\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (searchQuery.trim() || selectedCategory) {\n            performSearch();\n        }\n    }, [\n        searchQuery,\n        selectedCategory,\n        showAllFAQs,\n        initialLimit\n    ]);\n    /**\n   * Cargar datos iniciales\n   */ const loadInitialData = async ()=>{\n        const startTime = Date.now();\n        try {\n            setIsLoading(true);\n            const [categoriesData, popularFAQs, statsData] = await Promise.all([\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getCategories(),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(initialLimit),\n                _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQStats()\n            ]);\n            setCategories(categoriesData);\n            setFaqs(popularFAQs);\n            setStats(statsData);\n            // Registrar carga de sección\n            const loadTime = Date.now() - startTime;\n            _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackSectionLoad(\"faq-section\", loadTime);\n        } catch (error) {\n            console.error(\"Error loading FAQ data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Realizar búsqueda de FAQs\n   */ const performSearch = async ()=>{\n        try {\n            if (searchQuery.trim()) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].searchFAQs(searchQuery, {\n                    category: selectedCategory || undefined,\n                    limit: showAllFAQs ? 50 : initialLimit\n                });\n                setFaqs(results);\n            } else if (selectedCategory) {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getFAQsByCategory(selectedCategory, showAllFAQs ? undefined : initialLimit);\n                setFaqs(results);\n            } else {\n                const results = await _lib_services_faqService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPopularFAQs(showAllFAQs ? 50 : initialLimit);\n                setFaqs(results);\n            }\n        } catch (error) {\n            console.error(\"Error searching FAQs:\", error);\n        }\n    };\n    /**\n   * Alternar expansión de FAQ\n   */ const toggleFAQExpansion = (faqId)=>{\n        const newExpanded = new Set(expandedFAQs);\n        if (newExpanded.has(faqId)) {\n            newExpanded.delete(faqId);\n        } else {\n            newExpanded.add(faqId);\n            // Registrar visualización cuando se expande\n            const faq = faqs.find((f)=>f.id === faqId);\n            if (faq) {\n                _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackFAQView(faq.id, faq.question);\n            }\n        }\n        setExpandedFAQs(newExpanded);\n    };\n    /**\n   * Limpiar filtros\n   */ const clearFilters = ()=>{\n        setSearchQuery(\"\");\n        setSelectedCategory(\"\");\n        setShowAllFAQs(false);\n    };\n    /**\n   * Obtener icono de categoría\n   */ const getCategoryIcon = (iconName)=>{\n        const IconComponent = categoryIcons[iconName] || _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        return IconComponent;\n    };\n    // FAQs filtrados y limitados\n    const displayedFAQs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return showAllFAQs ? faqs : faqs.slice(0, initialLimit);\n    }, [\n        faqs,\n        showAllFAQs,\n        initialLimit\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-chia-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Cargando preguntas frecuentes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-8 w-8 text-chia-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalFAQs,\n                                    \" preguntas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    stats.totalCategories,\n                                    \" categor\\xedas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"M\\xe1s consultada: \",\n                                    stats.mostPopularCategory\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            (showSearch || showCategoryFilter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"Buscar en preguntas frecuentes...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 19\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 17\n                            }, this),\n                            showCategoryFilter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Filtrar por categor\\xeda:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: selectedCategory === \"\" ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSelectedCategory(\"\"),\n                                                className: \"h-8\",\n                                                children: \"Todas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this),\n                                            categories.map((category)=>{\n                                                const IconComponent = getCategoryIcon(category.icon);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setSelectedCategory(category.id);\n                                                        _lib_services_faqAnalytics__WEBPACK_IMPORTED_MODULE_7__[\"default\"].trackCategoryFilter(category.id);\n                                                    },\n                                                    className: \"h-8 space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"ml-1 h-4 text-xs\",\n                                                            children: category.count\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, category.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, this),\n                            (searchQuery || selectedCategory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: clearFilters,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Limpiar filtros\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: displayedFAQs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No se encontraron preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: searchQuery ? 'No hay resultados para \"'.concat(searchQuery, '\"') : \"No hay preguntas disponibles en esta categor\\xeda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this),\n                            (searchQuery || selectedCategory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: clearFilters,\n                                children: \"Ver todas las preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        displayedFAQs.map((faq)=>{\n                            const isExpanded = expandedFAQs.has(faq.id);\n                            const category = categories.find((cat)=>cat.id === faq.category);\n                            const IconComponent = category ? getCategoryIcon(category.icon) : _barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"transition-all duration-200 hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"cursor-pointer\",\n                                        onClick: ()=>toggleFAQExpansion(faq.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-4 w-4 text-chia-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: category === null || category === void 0 ? void 0 : category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        faq.popularity,\n                                                                        \"% popular\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-left text-lg leading-tight\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"ml-4 flex-shrink-0\",\n                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_ChevronDown_ChevronUp_CreditCard_FileCheck_FileText_Filter_HelpCircle_Receipt_Search_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this),\n                                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 25\n                                                }, this),\n                                                faq.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: faq.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 27\n                                                }, this),\n                                                faq.relatedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Tr\\xe1mites relacionados:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: faq.relatedProcedures.map((procedure, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-chia-blue-600\",\n                                                                    children: [\n                                                                        \"• \",\n                                                                        procedure\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 33\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, faq.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        !showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowAllFAQs(true),\n                                className: \"px-8\",\n                                children: [\n                                    \"Ver m\\xe1s preguntas (\",\n                                    faqs.length - initialLimit,\n                                    \" restantes)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 15\n                        }, this),\n                        showAllFAQs && faqs.length > initialLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                onClick: ()=>setShowAllFAQs(false),\n                                className: \"px-8\",\n                                children: \"Ver menos preguntas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\faq\\\\FAQSection.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQSection, \"oXoLY62DY6oxGQfx6srpVxVrg6w=\");\n_c = FAQSection;\nvar _c;\n$RefreshReg$(_c, \"FAQSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/faq/FAQSection.tsx\n"));

/***/ })

});